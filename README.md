# SDL Platform

基于Spring Boot + Vue3的企业级管理平台

## 🚀 快速开始

### Docker 部署（推荐）
```bash
# 一键部署，无需安装开发环境
./deploy-docker.sh

# 访问应用
# 前端：http://localhost
# 后端：http://localhost:8080
```

### 传统部署
```bash
# 需要Java 17+、Maven 3.6+、Node.js 16+
./build.sh
./deploy.sh
```

## 📚 文档

- [快速开始](docs/快速开始.md) - 部署和使用指南
- [Docker部署指南](docs/Docker部署指南.md) - 详细的Docker部署说明
- [项目结构](docs/项目结构.md) - 项目目录和模块说明
- [常见问题](docs/常见问题.md) - 问题排查和解决方案
- [OpenAPI使用指南](docs/OpenAPI使用指南.md) - API接口文档

## 🛠️ 开发

### 代码更新
```bash
# Docker环境下快速更新
./quick-update-docker.sh

# 查看服务状态
./quick-update-docker.sh status
```

### 技术栈
- **后端**: Spring Boot 3.3.5, MyBatis, MySQL
- **前端**: Vue 3.5.16, Element Plus, Vite
- **部署**: Docker, Nginx

## 📄 许可证

本项目采用 MIT 许可证