#!/bin/bash

# SDL Platform 开发模式脚本
# 启动热更新开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "SDL Platform 开发模式脚本"
    echo ""
    echo "用法: $0 <mode> [options]"
    echo ""
    echo "模式 (mode):"
    echo "  backend    - 启动后端开发服务器（热更新）"
    echo "  frontend   - 启动前端开发服务器（热更新）"
    echo "  both       - 同时启动前后端开发服务器"
    echo "  stop       - 停止开发服务器"
    echo ""
    echo "选项 (options):"
    echo "  --port <port>  - 指定前端开发服务器端口（默认3000）"
    echo "  --profile <profile> - 指定后端Spring配置文件（默认dev）"
    echo "  --help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 frontend              # 启动前端开发服务器"
    echo "  $0 backend --profile dev # 启动后端开发服务器"
    echo "  $0 both                  # 同时启动前后端"
    echo "  $0 stop                  # 停止所有开发服务器"
    echo ""
    echo "开发服务器地址:"
    echo "  前端: http://localhost:3000 (或指定端口)"
    echo "  后端: http://localhost:8080"
    echo ""
}

# 检查必要工具
check_requirements() {
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装，请安装 JDK 17+"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请安装 Maven 3.6+"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请安装 Node.js 16+"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        npm install -g pnpm --registry=https://registry.npmmirror.com
    fi
}

# 启动后端开发服务器
start_backend_dev() {
    local profile=${1:-dev}
    
    log_info "启动后端开发服务器..."
    log_info "Spring Profile: $profile"
    
    # 检查是否已有后端进程在运行
    if pgrep -f "spring-boot:run" > /dev/null; then
        log_warning "后端开发服务器已在运行"
        return
    fi
    
    # 启动后端开发服务器
    log_info "正在启动后端服务器，请稍候..."
    nohup mvn spring-boot:run -Dspring-boot.run.profiles=$profile -pl sdl-platform-admin > backend-dev.log 2>&1 &
    
    # 等待服务启动
    log_info "等待后端服务启动..."
    for i in {1..60}; do
        if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
            log_success "后端开发服务器启动成功"
            log_info "后端地址: http://localhost:8080"
            log_info "API文档: http://localhost:8080/swagger-ui.html"
            log_info "日志文件: backend-dev.log"
            return
        fi
        sleep 2
    done
    
    log_error "后端服务启动超时，请检查日志: backend-dev.log"
}

# 启动前端开发服务器
start_frontend_dev() {
    local port=${1:-3000}
    
    log_info "启动前端开发服务器..."
    log_info "端口: $port"
    
    # 检查是否已有前端进程在运行
    if pgrep -f "vite.*dev" > /dev/null; then
        log_warning "前端开发服务器已在运行"
        return
    fi
    
    cd sdl-platform-vue3
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        pnpm config set registry https://registry.npmmirror.com
        pnpm install
    fi
    
    # 启动前端开发服务器
    log_info "正在启动前端服务器，请稍候..."
    nohup pnpm run dev --port $port > ../frontend-dev.log 2>&1 &
    
    cd ..
    
    # 等待服务启动
    log_info "等待前端服务启动..."
    for i in {1..30}; do
        if curl -f http://localhost:$port >/dev/null 2>&1; then
            log_success "前端开发服务器启动成功"
            log_info "前端地址: http://localhost:$port"
            log_info "日志文件: frontend-dev.log"
            return
        fi
        sleep 2
    done
    
    log_error "前端服务启动超时，请检查日志: frontend-dev.log"
}

# 停止开发服务器
stop_dev_servers() {
    log_info "停止开发服务器..."
    
    # 停止后端开发服务器
    if pgrep -f "spring-boot:run" > /dev/null; then
        pkill -f "spring-boot:run"
        log_success "后端开发服务器已停止"
    fi
    
    # 停止前端开发服务器
    if pgrep -f "vite.*dev" > /dev/null; then
        pkill -f "vite.*dev"
        log_success "前端开发服务器已停止"
    fi
    
    # 清理日志文件
    rm -f backend-dev.log frontend-dev.log
    
    log_success "所有开发服务器已停止"
}

# 显示运行状态
show_status() {
    echo ""
    log_info "开发服务器状态:"
    
    # 检查后端状态
    if pgrep -f "spring-boot:run" > /dev/null; then
        echo -e "  后端: ${GREEN}运行中${NC} (http://localhost:8080)"
    else
        echo -e "  后端: ${RED}未运行${NC}"
    fi
    
    # 检查前端状态
    if pgrep -f "vite.*dev" > /dev/null; then
        echo -e "  前端: ${GREEN}运行中${NC} (http://localhost:3000)"
    else
        echo -e "  前端: ${RED}未运行${NC}"
    fi
    
    echo ""
}

# 主函数
main() {
    local mode=""
    local port=3000
    local profile="dev"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            backend|frontend|both|stop)
                mode="$1"
                shift
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --profile)
                profile="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查模式参数
    if [ -z "$mode" ]; then
        log_error "请指定模式: backend, frontend, both, 或 stop"
        show_help
        exit 1
    fi
    
    echo "========================================="
    echo "    SDL Platform 开发模式"
    echo "========================================="
    
    # 执行对应模式
    case $mode in
        "backend")
            check_requirements
            start_backend_dev $profile
            ;;
        "frontend")
            check_requirements
            start_frontend_dev $port
            ;;
        "both")
            check_requirements
            start_backend_dev $profile
            start_frontend_dev $port
            ;;
        "stop")
            stop_dev_servers
            ;;
    esac
    
    # 显示状态
    show_status
    
    if [ "$mode" != "stop" ]; then
        echo "提示:"
        echo "  - 开发服务器支持热更新，修改代码后会自动重新加载"
        echo "  - 查看日志: tail -f backend-dev.log 或 tail -f frontend-dev.log"
        echo "  - 停止服务器: $0 stop"
        echo ""
    fi
}

# 执行主函数
main "$@"
