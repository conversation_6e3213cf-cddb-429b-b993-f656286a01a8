# SDL Platform 后端部署镜像
FROM eclipse-temurin:17-jre

# 安装curl用于健康检查
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制已编译的jar文件
COPY deploy/backend/sdl-platform-admin.jar app.jar

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Dspring.profiles.active=public", "-jar", "app.jar"]
