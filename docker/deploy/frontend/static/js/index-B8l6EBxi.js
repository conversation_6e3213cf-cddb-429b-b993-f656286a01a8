import{_ as be,z as we,d as Ve,r as m,A as ke,I as Ce,e as u,F as J,c as X,o as v,G as g,f as l,H as Z,i as t,m as he,l as o,n as p,j as b,h as c,t as N,J as xe,K as Ae}from"./index-CdiCNU81.js";import{l as Ke,g as Pe,a as Se,d as Te,c as Ue,r as De,u as Ie,b as $e}from"./openapi-BtKbeQMh.js";const Re={class:"app-container"},Ye={class:"dialog-footer"},Ge={style:{"margin-bottom":"20px"}},Oe={class:"dialog-footer"},Le=we({name:"OpenApiKey"}),Ne=Object.assign(Le,{setup(Me){const{proxy:f}=Ve(),M=m([]),k=m(!1),T=m(!0),U=m(!0),D=m([]),q=m(!0),E=m(!0),I=m(0),$=m(""),R=m([]),A=m(!1),K=m(""),F=m([]),P=m([]),ee=ke({form:{},queryParams:{pageNum:1,pageSize:10,name:null,status:null,startCreateTime:null,endCreateTime:null},rules:{name:[{required:!0,message:"密钥名称不能为空",trigger:"blur"}]}}),{queryParams:s,form:i,rules:le}=Ce(ee);function te(n){n&&n.length===2?(s.value.startCreateTime=n[0],s.value.endCreateTime=n[1]):(s.value.startCreateTime=null,s.value.endCreateTime=null)}function w(){T.value=!0,Ke(s.value).then(n=>{M.value=n.rows,I.value=n.total,T.value=!1})}function ae(){Pe().then(n=>{F.value=n.data||["default"]})}function ne(){k.value=!1,Y()}function Y(){i.value={id:null,name:null,allowedGroups:null,ipWhitelist:null,rateLimit:100,expireTime:null,status:"0",remark:null},P.value=[],f.resetForm("openApiKeyRef")}function G(){s.value.pageNum=1,w()}function oe(){R.value=[],f.resetForm("queryRef"),G()}function ie(n){D.value=n.map(e=>e.id),q.value=n.length!=1,E.value=!n.length}function ue(){Y(),k.value=!0,$.value="添加OpenAPI密钥"}function H(n){Y();const e=n.id||D.value;Se(e).then(C=>{i.value=C.data,P.value=i.value.allowedGroups?i.value.allowedGroups.split(","):[],k.value=!0,$.value="修改OpenAPI密钥"})}function se(){f.$refs.openApiKeyRef.validate(n=>{n&&(i.value.allowedGroups=P.value.join(","),i.value.id!=null?Ie(i.value).then(e=>{f.$modal.msgSuccess("修改成功"),k.value=!1,w()}):$e(i.value).then(e=>{f.$modal.msgSuccess("新增成功"),k.value=!1,w(),e.data&&e.data.apiKey&&(K.value=e.data.apiKey,A.value=!0)}))})}function z(n){const e=n.id||D.value;f.$modal.confirm('是否确认删除OpenAPI密钥编号为"'+e+'"的数据项？').then(function(){return Te(e)}).then(()=>{w(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function de(){f.download("system/openapi/key/export",{...s.value},`openapi_key_${new Date().getTime()}.xlsx`)}function pe(n){let e=n.status==="0"?"启用":"停用";f.$modal.confirm('确认要"'+e+'""'+n.name+'"密钥吗？').then(function(){return Ue(n)}).then(()=>{f.$modal.msgSuccess(e+"成功")}).catch(function(){n.status=n.status==="0"?"1":"0"})}function re(n){f.$modal.confirm('重新生成密钥将使旧密钥失效，确认要重新生成"'+n.name+'"的API密钥吗？').then(function(){return De(n.id)}).then(e=>{f.$modal.msgSuccess("重新生成成功"),w(),K.value=e.data,A.value=!0}).catch(()=>{})}function B(n){const e=document.createElement("textarea");e.value=n,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),f.$modal.msgSuccess("API密钥已复制到剪贴板")}return w(),ae(),(n,e)=>{const C=u("el-input"),_=u("el-form-item"),d=u("el-col"),S=u("el-option"),O=u("el-select"),W=u("el-date-picker"),r=u("el-button"),h=u("el-row"),L=u("el-form"),me=u("right-toolbar"),y=u("el-table-column"),fe=u("el-switch"),_e=u("el-table"),ve=u("pagination"),j=u("el-divider"),ye=u("el-input-number"),Q=u("el-dialog"),ge=u("el-alert"),x=J("hasPermi"),ce=J("loading");return v(),X("div",Re,[g(l(L,{model:o(s),ref:"queryRef","label-width":"80px"},{default:t(()=>[l(h,{gutter:20},{default:t(()=>[l(d,{span:6},{default:t(()=>[l(_,{label:"密钥名称",prop:"name"},{default:t(()=>[l(C,{modelValue:o(s).name,"onUpdate:modelValue":e[0]||(e[0]=a=>o(s).name=a),placeholder:"请输入密钥名称",clearable:"",onKeyup:he(G,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:6},{default:t(()=>[l(_,{label:"状态",prop:"status"},{default:t(()=>[l(O,{modelValue:o(s).status,"onUpdate:modelValue":e[1]||(e[1]=a=>o(s).status=a),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:t(()=>[l(S,{label:"正常",value:"0"}),l(S,{label:"停用",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(d,{span:6},{default:t(()=>[l(_,{label:"创建时间",prop:"dateRange"},{default:t(()=>[l(W,{modelValue:R.value,"onUpdate:modelValue":e[2]||(e[2]=a=>R.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",onChange:te},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:6},{default:t(()=>[l(_,{style:{"margin-right":"5px"}},{default:t(()=>[l(r,{type:"primary",icon:"Search",onClick:G},{default:t(()=>e[18]||(e[18]=[p("搜索")])),_:1,__:[18]}),l(r,{icon:"Refresh",onClick:oe},{default:t(()=>e[19]||(e[19]=[p("重置")])),_:1,__:[19]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),[[Z,U.value]]),l(h,{gutter:10,class:"mb8"},{default:t(()=>[l(d,{span:1.5},{default:t(()=>[g((v(),b(r,{type:"primary",plain:"",icon:"Plus",onClick:ue},{default:t(()=>e[20]||(e[20]=[p("新增")])),_:1,__:[20]})),[[x,["system:openapi:key:add"]]])]),_:1}),l(d,{span:1.5},{default:t(()=>[g((v(),b(r,{type:"success",plain:"",icon:"Edit",disabled:q.value,onClick:H},{default:t(()=>e[21]||(e[21]=[p("修改")])),_:1,__:[21]},8,["disabled"])),[[x,["system:openapi:key:edit"]]])]),_:1}),l(d,{span:1.5},{default:t(()=>[g((v(),b(r,{type:"danger",plain:"",icon:"Delete",disabled:E.value,onClick:z},{default:t(()=>e[22]||(e[22]=[p("删除")])),_:1,__:[22]},8,["disabled"])),[[x,["system:openapi:key:remove"]]])]),_:1}),l(d,{span:1.5},{default:t(()=>[g((v(),b(r,{type:"warning",plain:"",icon:"Download",onClick:de},{default:t(()=>e[23]||(e[23]=[p("导出")])),_:1,__:[23]})),[[x,["system:openapi:key:export"]]])]),_:1}),l(me,{showSearch:U.value,"onUpdate:showSearch":e[3]||(e[3]=a=>U.value=a),onQueryTable:w},null,8,["showSearch"])]),_:1}),g((v(),b(_e,{data:M.value,onSelectionChange:ie},{default:t(()=>[l(y,{type:"selection",width:"55",align:"center"}),l(y,{label:"密钥名称",align:"center",prop:"name","show-overflow-tooltip":""}),l(y,{label:"API密钥",align:"center",prop:"apiKey","show-overflow-tooltip":"",width:"240"},{default:t(a=>{var V;return[c("span",null,N((V=a.row.apiKey)==null?void 0:V.substring(0,20))+"...",1),l(r,{link:"",type:"primary",icon:"DocumentCopy",onClick:qe=>B(a.row.apiKey),title:"复制完整密钥"},null,8,["onClick"])]}),_:1}),l(y,{label:"权限组",align:"center",prop:"allowedGroups","show-overflow-tooltip":"",width:"160"}),l(y,{label:"状态",align:"center",prop:"status",width:"100"},{default:t(a=>[l(fe,{modelValue:a.row.status,"onUpdate:modelValue":V=>a.row.status=V,"active-value":"0","inactive-value":"1",onChange:V=>pe(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(y,{label:"限流次数/小时",align:"center",prop:"rateLimit",width:"140"}),l(y,{label:"总访问次数",align:"center",prop:"totalAccessCount",width:"120"}),l(y,{label:"最后访问时间",align:"center",prop:"lastAccessTime",width:"160"},{default:t(a=>[c("span",null,N(n.parseTime(a.row.lastAccessTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(y,{label:"过期时间",align:"center",prop:"expireTime",width:"160"},{default:t(a=>[c("span",null,N(n.parseTime(a.row.expireTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(y,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"220"},{default:t(a=>[g((v(),b(r,{link:"",type:"primary",icon:"Edit",onClick:V=>H(a.row)},{default:t(()=>e[24]||(e[24]=[p("修改")])),_:2,__:[24]},1032,["onClick"])),[[x,["system:openapi:key:edit"]]]),g((v(),b(r,{link:"",type:"primary",icon:"Refresh",onClick:V=>re(a.row)},{default:t(()=>e[25]||(e[25]=[p("重置")])),_:2,__:[25]},1032,["onClick"])),[[x,["system:openapi:key:edit"]]]),g((v(),b(r,{link:"",type:"primary",icon:"Delete",onClick:V=>z(a.row)},{default:t(()=>e[26]||(e[26]=[p("删除")])),_:2,__:[26]},1032,["onClick"])),[[x,["system:openapi:key:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,T.value]]),g(l(ve,{total:I.value,page:o(s).pageNum,"onUpdate:page":e[4]||(e[4]=a=>o(s).pageNum=a),limit:o(s).pageSize,"onUpdate:limit":e[5]||(e[5]=a=>o(s).pageSize=a),onPagination:w},null,8,["total","page","limit"]),[[Z,I.value>0]]),l(Q,{title:$.value,modelValue:k.value,"onUpdate:modelValue":e[13]||(e[13]=a=>k.value=a),width:"800px","append-to-body":""},{footer:t(()=>[c("div",Ye,[l(r,{type:"primary",onClick:se},{default:t(()=>e[33]||(e[33]=[p("确 定")])),_:1,__:[33]}),l(r,{onClick:ne},{default:t(()=>e[34]||(e[34]=[p("取 消")])),_:1,__:[34]})])]),default:t(()=>[l(L,{ref:"openApiKeyRef",model:o(i),rules:o(le),"label-width":"100px"},{default:t(()=>[l(j,{"content-position":"left"},{default:t(()=>e[27]||(e[27]=[p("基本信息")])),_:1,__:[27]}),l(h,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(_,{label:"密钥名称",prop:"name"},{default:t(()=>[l(C,{modelValue:o(i).name,"onUpdate:modelValue":e[6]||(e[6]=a=>o(i).name=a),placeholder:"请输入密钥名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(_,{label:"状态",prop:"status"},{default:t(()=>[l(O,{modelValue:o(i).status,"onUpdate:modelValue":e[7]||(e[7]=a=>o(i).status=a),placeholder:"请选择状态",style:{width:"100%"}},{default:t(()=>[l(S,{label:"正常",value:"0"}),l(S,{label:"停用",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:20},{default:t(()=>[l(d,{span:24},{default:t(()=>[l(_,{label:"权限组",prop:"allowedGroups"},{default:t(()=>[l(O,{modelValue:P.value,"onUpdate:modelValue":e[8]||(e[8]=a=>P.value=a),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入权限组",style:{width:"100%"}},{default:t(()=>[(v(!0),X(xe,null,Ae(F.value,a=>(v(),b(S,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e[28]||(e[28]=c("div",{class:"form-tip"},"多个权限组用逗号分隔，可选择预设组或自定义输入",-1))]),_:1,__:[28]})]),_:1})]),_:1}),l(j,{"content-position":"left"},{default:t(()=>e[29]||(e[29]=[p("安全配置")])),_:1,__:[29]}),l(h,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(_,{label:"限流次数",prop:"rateLimit"},{default:t(()=>[l(ye,{modelValue:o(i).rateLimit,"onUpdate:modelValue":e[9]||(e[9]=a=>o(i).rateLimit=a),min:1,max:1e4,placeholder:"每小时请求次数限制",style:{width:"100%"}},null,8,["modelValue"]),e[30]||(e[30]=c("div",{class:"form-tip"},"每小时允许的最大请求次数",-1))]),_:1,__:[30]})]),_:1}),l(d,{span:12},{default:t(()=>[l(_,{label:"过期时间",prop:"expireTime"},{default:t(()=>[l(W,{modelValue:o(i).expireTime,"onUpdate:modelValue":e[10]||(e[10]=a=>o(i).expireTime=a),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),e[31]||(e[31]=c("div",{class:"form-tip"},"留空表示永不过期",-1))]),_:1,__:[31]})]),_:1})]),_:1}),l(h,{gutter:20},{default:t(()=>[l(d,{span:24},{default:t(()=>[l(_,{label:"IP白名单",prop:"ipWhitelist"},{default:t(()=>[l(C,{modelValue:o(i).ipWhitelist,"onUpdate:modelValue":e[11]||(e[11]=a=>o(i).ipWhitelist=a),type:"textarea",placeholder:"请输入IP白名单，多个IP用逗号分隔，支持CIDR格式，如：***********,10.0.0.0/24",rows:3},null,8,["modelValue"]),e[32]||(e[32]=c("div",{class:"form-tip"},"留空表示允许所有IP访问",-1))]),_:1,__:[32]})]),_:1})]),_:1}),l(h,{gutter:20},{default:t(()=>[l(d,{span:24},{default:t(()=>[l(_,{label:"备注",prop:"remark"},{default:t(()=>[l(C,{modelValue:o(i).remark,"onUpdate:modelValue":e[12]||(e[12]=a=>o(i).remark=a),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(Q,{title:"API密钥",modelValue:A.value,"onUpdate:modelValue":e[17]||(e[17]=a=>A.value=a),width:"600px","append-to-body":""},{footer:t(()=>[c("div",Oe,[l(r,{type:"primary",onClick:e[15]||(e[15]=a=>B(K.value))},{default:t(()=>e[35]||(e[35]=[p("复制密钥")])),_:1,__:[35]}),l(r,{onClick:e[16]||(e[16]=a=>A.value=!1)},{default:t(()=>e[36]||(e[36]=[p("关 闭")])),_:1,__:[36]})])]),default:t(()=>[c("div",Ge,[l(ge,{title:"请妥善保管API密钥",description:"API密钥具有完整的接口访问权限，请勿泄露给他人。建议定期更换密钥以确保安全。",type:"warning",closable:!1})]),l(L,{"label-width":"80px"},{default:t(()=>[l(_,{label:"API密钥"},{default:t(()=>[l(C,{modelValue:K.value,"onUpdate:modelValue":e[14]||(e[14]=a=>K.value=a),readonly:"",rows:3,style:{"font-family":"monospace"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),He=be(Ne,[["__scopeId","data-v-c0d81cf3"]]);export{He as default};
