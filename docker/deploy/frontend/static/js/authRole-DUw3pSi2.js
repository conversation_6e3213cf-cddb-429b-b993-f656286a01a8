import{z as F,u as K,d as O,r as i,D as G,e as s,F as H,c as P,o as h,h as c,f as t,G as C,i as a,l as o,j as q,t as S,H as J,C as V,n as $}from"./index-CdiCNU81.js";import{a as L,u as M}from"./user-CV6IIF7D.js";const Q={class:"app-container"},W={style:{"text-align":"center","margin-left":"-120px","margin-top":"30px"}},X=F({name:"AuthRole"}),te=Object.assign(X,{setup(Y){const v=K(),{proxy:f}=O(),g=i(!0),b=i(0),u=i(1),d=i(10),w=i([]),_=i([]),r=i({nickName:void 0,userName:void 0,userId:void 0});function T(l){k(l)&&f.$refs.roleRef.toggleRowSelection(l)}function j(l){w.value=l.map(e=>e.roleId)}function B(l){return l.roleId}function k(l){return l.status==="0"}function I(){const l={path:"/system/user"};f.$tab.closeOpenPage(l)}function D(){const l=r.value.userId,e=w.value.join(",");M({userId:l,roleIds:e}).then(p=>{f.$modal.msgSuccess("授权成功"),I()})}return(()=>{const l=v.params&&v.params.userId;l&&(g.value=!0,L(l).then(e=>{r.value=e.user,_.value=e.roles,b.value=_.value.length,G(()=>{_.value.forEach(p=>{p.flag&&f.$refs.roleRef.toggleRowSelection(p)})}),g.value=!1}))})(),(l,e)=>{const p=s("el-input"),N=s("el-form-item"),R=s("el-col"),U=s("el-row"),x=s("el-form"),m=s("el-table-column"),A=s("el-table"),z=s("pagination"),y=s("el-button"),E=H("loading");return h(),P("div",Q,[e[8]||(e[8]=c("h4",{class:"form-header h4"},"基本信息",-1)),t(x,{model:o(r),"label-width":"80px"},{default:a(()=>[t(U,null,{default:a(()=>[t(R,{span:8,offset:2},{default:a(()=>[t(N,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(p,{modelValue:o(r).nickName,"onUpdate:modelValue":e[0]||(e[0]=n=>o(r).nickName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(R,{span:8,offset:2},{default:a(()=>[t(N,{label:"登录账号",prop:"userName"},{default:a(()=>[t(p,{modelValue:o(r).userName,"onUpdate:modelValue":e[1]||(e[1]=n=>o(r).userName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e[9]||(e[9]=c("h4",{class:"form-header h4"},"角色信息",-1)),C((h(),q(A,{"row-key":B,onRowClick:T,ref:"roleRef",onSelectionChange:j,data:o(_).slice((o(u)-1)*o(d),o(u)*o(d))},{default:a(()=>[t(m,{label:"序号",width:"55",type:"index",align:"center"},{default:a(n=>[c("span",null,S((o(u)-1)*o(d)+n.$index+1),1)]),_:1}),t(m,{type:"selection","reserve-selection":!0,selectable:k,width:"55"}),t(m,{label:"角色编号",align:"center",prop:"roleId"}),t(m,{label:"角色名称",align:"center",prop:"roleName"}),t(m,{label:"权限字符",align:"center",prop:"roleKey"}),t(m,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(n=>[c("span",null,S(l.parseTime(n.row.createTime)),1)]),_:1})]),_:1},8,["data"])),[[E,o(g)]]),C(t(z,{total:o(b),page:o(u),"onUpdate:page":e[2]||(e[2]=n=>V(u)?u.value=n:null),limit:o(d),"onUpdate:limit":e[3]||(e[3]=n=>V(d)?d.value=n:null)},null,8,["total","page","limit"]),[[J,o(b)>0]]),t(x,{"label-width":"100px"},{default:a(()=>[c("div",W,[t(y,{type:"primary",onClick:e[4]||(e[4]=n=>D())},{default:a(()=>e[6]||(e[6]=[$("提交")])),_:1,__:[6]}),t(y,{onClick:e[5]||(e[5]=n=>I())},{default:a(()=>e[7]||(e[7]=[$("返回")])),_:1,__:[7]})])]),_:1})])}}});export{te as default};
