import{_ as o,x as l,e as _,c,o as n,h as t,y as d,f as r,t as p,l as m,i as u,n as v}from"./index-CdiCNU81.js";const h="/static/png/404-N4aRkdWY.png",a="/static/png/404_cloud-CPexjtDj.png",b={class:"wscn-http404-container"},g={class:"wscn-http404"},f={class:"bullshit"},x={class:"bullshit__headline"},k={__name:"404",setup(N){let i=l(()=>"找不到网页！");return(V,s)=>{const e=_("router-link");return n(),c("div",b,[t("div",g,[s[3]||(s[3]=d('<div class="pic-404" data-v-5945313b><img class="pic-404__parent" src="'+h+'" alt="404" data-v-5945313b><img class="pic-404__child left" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child right" src="'+a+'" alt="404" data-v-5945313b></div>',1)),t("div",f,[s[1]||(s[1]=t("div",{class:"bullshit__oops"}," 404错误! ",-1)),t("div",x,p(m(i)),1),s[2]||(s[2]=t("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),r(e,{to:"/index",class:"bullshit__return-home"},{default:u(()=>s[0]||(s[0]=[v(" 返回首页 ")])),_:1,__:[0]})])])])}}},B=o(k,[["__scopeId","data-v-5945313b"]]);export{B as default};
