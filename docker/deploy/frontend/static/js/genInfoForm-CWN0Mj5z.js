import{r as U,d as L,B as $,w as B,e as o,j as y,o as f,i as l,f as e,c as v,k as I,n as u,l as q,h as F,J as C,G as Q,K as T,H as R}from"./index-CdiCNU81.js";import{l as X}from"./menu-BcsgTeEp.js";const _={__name:"genInfoForm",props:{info:{type:Object,default:null},tables:{type:Array,default:null}},setup(a){const P=U([]),x=U([]),{proxy:M}=L(),V=a,S=U({tplCategory:[{required:!0,message:"请选择生成模板",trigger:"blur"}],packageName:[{required:!0,message:"请输入生成包路径",trigger:"blur"}],moduleName:[{required:!0,message:"请输入生成模块名",trigger:"blur"}],businessName:[{required:!0,message:"请输入生成业务名",trigger:"blur"}],functionName:[{required:!0,message:"请输入生成功能名",trigger:"blur"}]});function j(b){V.info.subTableFkName=""}function E(b){b!=="sub"&&(V.info.subTableName="",V.info.subTableFkName="")}function z(b){for(var n in V.tables){const r=V.tables[n].tableName;if(b===r){P.value=V.tables[n].columns;break}}}function O(){X().then(b=>{x.value=M.handleTree(b.data,"menuId")})}return $(()=>{O()}),B(()=>V.info.subTableName,b=>{z(b)}),B(()=>V.info.tplWebType,b=>{b===""&&(V.info.tplWebType="element-plus")}),(b,n)=>{const r=o("el-option"),N=o("el-select"),d=o("el-form-item"),m=o("el-col"),i=o("question-filled"),s=o("el-icon"),p=o("el-tooltip"),k=o("el-input"),W=o("el-radio"),A=o("el-tree-select"),D=o("el-button"),G=o("el-dropdown-item"),H=o("el-dropdown-menu"),J=o("el-dropdown"),w=o("el-row"),K=o("el-form");return f(),y(K,{ref:"genInfoForm",model:a.info,rules:q(S),"label-width":"150px"},{default:l(()=>[e(w,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{prop:"tplCategory"},{label:l(()=>n[16]||(n[16]=[u("生成模板")])),default:l(()=>[e(N,{modelValue:a.info.tplCategory,"onUpdate:modelValue":n[0]||(n[0]=t=>a.info.tplCategory=t),onChange:E},{default:l(()=>[e(r,{label:"单表（增删改查）",value:"crud"}),e(r,{label:"树表（增删改查）",value:"tree"}),e(r,{label:"主子表（增删改查）",value:"sub"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"tplWebType"},{label:l(()=>n[17]||(n[17]=[u("前端类型")])),default:l(()=>[e(N,{modelValue:a.info.tplWebType,"onUpdate:modelValue":n[1]||(n[1]=t=>a.info.tplWebType=t)},{default:l(()=>[e(r,{label:"Vue2 Element UI 模版",value:"element-ui"}),e(r,{label:"Vue3 Element Plus 模版",value:"element-plus"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"packageName"},{label:l(()=>[n[18]||(n[18]=u(" 生成包路径 ")),e(p,{content:"生成在哪个java包下，例如 com.ruoyi.system",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(k,{modelValue:a.info.packageName,"onUpdate:modelValue":n[2]||(n[2]=t=>a.info.packageName=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"moduleName"},{label:l(()=>[n[19]||(n[19]=u(" 生成模块名 ")),e(p,{content:"可理解为子系统名，例如 system",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(k,{modelValue:a.info.moduleName,"onUpdate:modelValue":n[3]||(n[3]=t=>a.info.moduleName=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"businessName"},{label:l(()=>[n[20]||(n[20]=u(" 生成业务名 ")),e(p,{content:"可理解为功能英文名，例如 user",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(k,{modelValue:a.info.businessName,"onUpdate:modelValue":n[4]||(n[4]=t=>a.info.businessName=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"functionName"},{label:l(()=>[n[21]||(n[21]=u(" 生成功能名 ")),e(p,{content:"用作类描述，例如 用户",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(k,{modelValue:a.info.functionName,"onUpdate:modelValue":n[5]||(n[5]=t=>a.info.functionName=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{prop:"genType"},{label:l(()=>[n[22]||(n[22]=u(" 生成代码方式 ")),e(p,{content:"默认为zip压缩包下载，也可以自定义生成路径",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(W,{modelValue:a.info.genType,"onUpdate:modelValue":n[6]||(n[6]=t=>a.info.genType=t),value:"0"},{default:l(()=>n[23]||(n[23]=[u("zip压缩包")])),_:1,__:[23]},8,["modelValue"]),e(W,{modelValue:a.info.genType,"onUpdate:modelValue":n[7]||(n[7]=t=>a.info.genType=t),value:"1"},{default:l(()=>n[24]||(n[24]=[u("自定义路径")])),_:1,__:[24]},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[25]||(n[25]=u(" 上级菜单 ")),e(p,{content:"分配到指定菜单下，例如 系统管理",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(A,{modelValue:a.info.parentMenuId,"onUpdate:modelValue":n[8]||(n[8]=t=>a.info.parentMenuId=t),data:q(x),props:{value:"menuId",label:"menuName",children:"children"},"value-key":"menuId",placeholder:"请选择系统菜单","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1}),a.info.genType=="1"?(f(),y(m,{key:0,span:24},{default:l(()=>[e(d,{prop:"genPath"},{label:l(()=>[n[26]||(n[26]=u(" 自定义路径 ")),e(p,{content:"填写磁盘绝对路径，若不填写，则生成到当前Web项目下",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(k,{modelValue:a.info.genPath,"onUpdate:modelValue":n[10]||(n[10]=t=>a.info.genPath=t)},{append:l(()=>[e(J,null,{dropdown:l(()=>[e(H,null,{default:l(()=>[e(G,{onClick:n[9]||(n[9]=t=>a.info.genPath="/")},{default:l(()=>n[28]||(n[28]=[u("恢复默认的生成基础路径")])),_:1,__:[28]})]),_:1})]),default:l(()=>[e(D,{type:"primary"},{default:l(()=>n[27]||(n[27]=[u(" 最近路径快速选择 "),F("i",{class:"el-icon-arrow-down el-icon--right"},null,-1)])),_:1,__:[27]})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0)]),_:1}),a.info.tplCategory=="tree"?(f(),v(C,{key:0},[n[32]||(n[32]=F("h4",{class:"form-header"},"其他信息",-1)),Q(e(w,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[29]||(n[29]=u(" 树编码字段 ")),e(p,{content:"树显示的编码字段名， 如：dept_id",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(N,{modelValue:a.info.treeCode,"onUpdate:modelValue":n[11]||(n[11]=t=>a.info.treeCode=t),placeholder:"请选择"},{default:l(()=>[(f(!0),v(C,null,T(a.info.columns,(t,g)=>(f(),y(r,{key:g,label:t.columnName+"："+t.columnComment,value:t.columnName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[30]||(n[30]=u(" 树父编码字段 ")),e(p,{content:"树显示的父编码字段名， 如：parent_Id",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(N,{modelValue:a.info.treeParentCode,"onUpdate:modelValue":n[12]||(n[12]=t=>a.info.treeParentCode=t),placeholder:"请选择"},{default:l(()=>[(f(!0),v(C,null,T(a.info.columns,(t,g)=>(f(),y(r,{key:g,label:t.columnName+"："+t.columnComment,value:t.columnName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[31]||(n[31]=u(" 树名称字段 ")),e(p,{content:"树节点的显示名称字段名， 如：dept_name",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(N,{modelValue:a.info.treeName,"onUpdate:modelValue":n[13]||(n[13]=t=>a.info.treeName=t),placeholder:"请选择"},{default:l(()=>[(f(!0),v(C,null,T(a.info.columns,(t,g)=>(f(),y(r,{key:g,label:t.columnName+"："+t.columnComment,value:t.columnName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},512),[[R,a.info.tplCategory=="tree"]])],64)):I("",!0),a.info.tplCategory=="sub"?(f(),v(C,{key:1},[n[35]||(n[35]=F("h4",{class:"form-header"},"关联信息",-1)),e(w,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[33]||(n[33]=u(" 关联子表的表名 ")),e(p,{content:"关联子表的表名， 如：sys_user",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(N,{modelValue:a.info.subTableName,"onUpdate:modelValue":n[14]||(n[14]=t=>a.info.subTableName=t),placeholder:"请选择",onChange:j},{default:l(()=>[(f(!0),v(C,null,T(a.tables,(t,g)=>(f(),y(r,{key:g,label:t.tableName+"："+t.tableComment,value:t.tableName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,null,{label:l(()=>[n[34]||(n[34]=u(" 子表关联的外键名 ")),e(p,{content:"子表关联的外键名， 如：user_id",placement:"top"},{default:l(()=>[e(s,null,{default:l(()=>[e(i)]),_:1})]),_:1})]),default:l(()=>[e(N,{modelValue:a.info.subTableFkName,"onUpdate:modelValue":n[15]||(n[15]=t=>a.info.subTableFkName=t),placeholder:"请选择"},{default:l(()=>[(f(!0),v(C,null,T(q(P),(t,g)=>(f(),y(r,{key:g,label:t.columnName+"："+t.columnComment,value:t.columnName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})],64)):I("",!0)]),_:1},8,["model","rules"])}}};export{_ as default};
