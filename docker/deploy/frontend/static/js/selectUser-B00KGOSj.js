import{z as A,d as P,r as c,A as Q,e as s,j as E,o as F,i as l,f as o,l as n,m as h,n as _,G,h as N,t as H,H as O,C as J}from"./index-CdiCNU81.js";import{u as M,d as W}from"./role-DqtqZXxX.js";const X={class:"dialog-footer"},Y=A({name:"SelectUser"}),oe=Object.assign(Y,{props:{roleId:{type:[Number,String]}},emits:["ok"],setup(S,{expose:C,emit:I}){const V=S,{proxy:p}=P(),{sys_normal_disable:k}=p.useDict("sys_normal_disable"),v=c([]),i=c(!1),g=c(0),y=c([]),a=Q({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,phonenumber:void 0});function x(){a.roleId=V.roleId,b(),i.value=!0}function U(r){p.$refs.refTable.toggleRowSelection(r)}function R(r){y.value=r.map(e=>e.userId)}function b(){M(a).then(r=>{v.value=r.rows,g.value=r.total})}function d(){a.pageNum=1,b()}function T(){p.resetForm("queryRef"),d()}const z=I;function $(){const r=a.roleId,e=y.value.join(",");if(e==""){p.$modal.msgError("请选择要分配的用户");return}W({roleId:r,userIds:e}).then(m=>{p.$modal.msgSuccess(m.msg),i.value=!1,z("ok")})}return C({show:x}),(r,e)=>{const m=s("el-input"),w=s("el-form-item"),f=s("el-button"),j=s("el-form"),u=s("el-table-column"),q=s("dict-tag"),B=s("el-table"),D=s("pagination"),K=s("el-row"),L=s("el-dialog");return F(),E(L,{title:"选择用户",modelValue:n(i),"onUpdate:modelValue":e[5]||(e[5]=t=>J(i)?i.value=t:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[N("div",X,[o(f,{type:"primary",onClick:$},{default:l(()=>e[8]||(e[8]=[_("确 定")])),_:1,__:[8]}),o(f,{onClick:e[4]||(e[4]=t=>i.value=!1)},{default:l(()=>e[9]||(e[9]=[_("取 消")])),_:1,__:[9]})])]),default:l(()=>[o(j,{model:n(a),ref:"queryRef",inline:!0},{default:l(()=>[o(w,{label:"用户名称",prop:"userName"},{default:l(()=>[o(m,{modelValue:n(a).userName,"onUpdate:modelValue":e[0]||(e[0]=t=>n(a).userName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"180px"},onKeyup:h(d,["enter"])},null,8,["modelValue"])]),_:1}),o(w,{label:"手机号码",prop:"phonenumber"},{default:l(()=>[o(m,{modelValue:n(a).phonenumber,"onUpdate:modelValue":e[1]||(e[1]=t=>n(a).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"180px"},onKeyup:h(d,["enter"])},null,8,["modelValue"])]),_:1}),o(w,null,{default:l(()=>[o(f,{type:"primary",icon:"Search",onClick:d},{default:l(()=>e[6]||(e[6]=[_("搜索")])),_:1,__:[6]}),o(f,{icon:"Refresh",onClick:T},{default:l(()=>e[7]||(e[7]=[_("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),o(K,null,{default:l(()=>[o(B,{onRowClick:U,ref:"refTable",data:n(v),onSelectionChange:R,height:"260px"},{default:l(()=>[o(u,{type:"selection",width:"55"}),o(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),o(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),o(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),o(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),o(u,{label:"状态",align:"center",prop:"status"},{default:l(t=>[o(q,{options:n(k),value:t.row.status},null,8,["options","value"])]),_:1}),o(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(t=>[N("span",null,H(r.parseTime(t.row.createTime)),1)]),_:1})]),_:1},8,["data"]),G(o(D,{total:n(g),page:n(a).pageNum,"onUpdate:page":e[2]||(e[2]=t=>n(a).pageNum=t),limit:n(a).pageSize,"onUpdate:limit":e[3]||(e[3]=t=>n(a).pageSize=t),onPagination:b},null,8,["total","page","limit"]),[[O,n(g)>0]])]),_:1})]),_:1},8,["modelValue"])}}});export{oe as default};
