import{S as C,z as _e,d as ye,r as m,A as ge,I as ve,e as a,F as J,c as N,o as d,G as b,f as t,H as O,l as o,i as n,m as M,J as $,K as q,j as f,n as p,C as W,h as X,t as Y}from"./index-CdiCNU81.js";function be(r){return C({url:"/system/notice/list",method:"get",params:r})}function we(r){return C({url:"/system/notice/"+r,method:"get"})}function Ve(r){return C({url:"/system/notice",method:"post",data:r})}function Te(r){return C({url:"/system/notice",method:"put",data:r})}function he(r){return C({url:"/system/notice/"+r,method:"delete"})}const Ce={class:"app-container"},ke={class:"dialog-footer"},Se=_e({name:"Notice"}),Ue=Object.assign(Se,{setup(r){const{proxy:_}=ye(),{sys_notice_status:P,sys_notice_type:U}=_.useDict("sys_notice_status","sys_notice_type"),F=m([]),y=m(!1),x=m(!0),k=m(!0),I=m([]),z=m(!0),K=m(!0),B=m(0),R=m(""),Z=ge({form:{},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},rules:{noticeTitle:[{required:!0,message:"公告标题不能为空",trigger:"blur"}],noticeType:[{required:!0,message:"公告类型不能为空",trigger:"change"}]}}),{queryParams:s,form:i,rules:ee}=ve(Z);function w(){x.value=!0,be(s.value).then(u=>{F.value=u.rows,B.value=u.total,x.value=!1})}function te(){y.value=!1,D()}function D(){i.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},_.resetForm("noticeRef")}function S(){s.value.pageNum=1,w()}function le(){_.resetForm("queryRef"),S()}function ne(u){I.value=u.map(e=>e.noticeId),z.value=u.length!=1,K.value=!u.length}function oe(){D(),y.value=!0,R.value="添加公告"}function E(u){D();const e=u.noticeId||I.value;we(e).then(T=>{i.value=T.data,y.value=!0,R.value="修改公告"})}function ae(){_.$refs.noticeRef.validate(u=>{u&&(i.value.noticeId!=null?Te(i.value).then(e=>{_.$modal.msgSuccess("修改成功"),y.value=!1,w()}):Ve(i.value).then(e=>{_.$modal.msgSuccess("新增成功"),y.value=!1,w()}))})}function L(u){const e=u.noticeId||I.value;_.$modal.confirm('是否确认删除公告编号为"'+e+'"的数据项？').then(function(){return he(e)}).then(()=>{w(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}return w(),(u,e)=>{const T=a("el-input"),g=a("el-form-item"),Q=a("el-option"),j=a("el-select"),c=a("el-button"),A=a("el-form"),V=a("el-col"),ie=a("right-toolbar"),G=a("el-row"),v=a("el-table-column"),H=a("dict-tag"),ue=a("el-table"),se=a("pagination"),de=a("el-radio"),re=a("el-radio-group"),pe=a("editor"),ce=a("el-dialog"),h=J("hasPermi"),me=J("loading");return d(),N("div",Ce,[b(t(A,{model:o(s),ref:"queryRef",inline:!0},{default:n(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:n(()=>[t(T,{modelValue:o(s).noticeTitle,"onUpdate:modelValue":e[0]||(e[0]=l=>o(s).noticeTitle=l),placeholder:"请输入公告标题",clearable:"",style:{width:"200px"},onKeyup:M(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"操作人员",prop:"createBy"},{default:n(()=>[t(T,{modelValue:o(s).createBy,"onUpdate:modelValue":e[1]||(e[1]=l=>o(s).createBy=l),placeholder:"请输入操作人员",clearable:"",style:{width:"200px"},onKeyup:M(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"类型",prop:"noticeType"},{default:n(()=>[t(j,{modelValue:o(s).noticeType,"onUpdate:modelValue":e[2]||(e[2]=l=>o(s).noticeType=l),placeholder:"公告类型",clearable:"",style:{width:"200px"}},{default:n(()=>[(d(!0),N($,null,q(o(U),l=>(d(),f(Q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,null,{default:n(()=>[t(c,{type:"primary",icon:"Search",onClick:S},{default:n(()=>e[11]||(e[11]=[p("搜索")])),_:1,__:[11]}),t(c,{icon:"Refresh",onClick:le},{default:n(()=>e[12]||(e[12]=[p("重置")])),_:1,__:[12]})]),_:1})]),_:1},8,["model"]),[[O,o(k)]]),t(G,{gutter:10,class:"mb8"},{default:n(()=>[t(V,{span:1.5},{default:n(()=>[b((d(),f(c,{type:"primary",plain:"",icon:"Plus",onClick:oe},{default:n(()=>e[13]||(e[13]=[p("新增")])),_:1,__:[13]})),[[h,["system:notice:add"]]])]),_:1}),t(V,{span:1.5},{default:n(()=>[b((d(),f(c,{type:"success",plain:"",icon:"Edit",disabled:o(z),onClick:E},{default:n(()=>e[14]||(e[14]=[p("修改")])),_:1,__:[14]},8,["disabled"])),[[h,["system:notice:edit"]]])]),_:1}),t(V,{span:1.5},{default:n(()=>[b((d(),f(c,{type:"danger",plain:"",icon:"Delete",disabled:o(K),onClick:L},{default:n(()=>e[15]||(e[15]=[p("删除")])),_:1,__:[15]},8,["disabled"])),[[h,["system:notice:remove"]]])]),_:1}),t(ie,{showSearch:o(k),"onUpdate:showSearch":e[3]||(e[3]=l=>W(k)?k.value=l:null),onQueryTable:w},null,8,["showSearch"])]),_:1}),b((d(),f(ue,{data:o(F),onSelectionChange:ne},{default:n(()=>[t(v,{type:"selection",width:"55",align:"center"}),t(v,{label:"序号",align:"center",prop:"noticeId",width:"100"}),t(v,{label:"公告标题",align:"center",prop:"noticeTitle","show-overflow-tooltip":!0}),t(v,{label:"公告类型",align:"center",prop:"noticeType",width:"100"},{default:n(l=>[t(H,{options:o(U),value:l.row.noticeType},null,8,["options","value"])]),_:1}),t(v,{label:"状态",align:"center",prop:"status",width:"100"},{default:n(l=>[t(H,{options:o(P),value:l.row.status},null,8,["options","value"])]),_:1}),t(v,{label:"创建者",align:"center",prop:"createBy",width:"100"}),t(v,{label:"创建时间",align:"center",prop:"createTime",width:"100"},{default:n(l=>[X("span",null,Y(u.parseTime(l.row.createTime,"{y}-{m}-{d}")),1)]),_:1}),t(v,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(l=>[b((d(),f(c,{link:"",type:"primary",icon:"Edit",onClick:fe=>E(l.row)},{default:n(()=>e[16]||(e[16]=[p("修改")])),_:2,__:[16]},1032,["onClick"])),[[h,["system:notice:edit"]]]),b((d(),f(c,{link:"",type:"primary",icon:"Delete",onClick:fe=>L(l.row)},{default:n(()=>e[17]||(e[17]=[p("删除")])),_:2,__:[17]},1032,["onClick"])),[[h,["system:notice:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,o(x)]]),b(t(se,{total:o(B),page:o(s).pageNum,"onUpdate:page":e[4]||(e[4]=l=>o(s).pageNum=l),limit:o(s).pageSize,"onUpdate:limit":e[5]||(e[5]=l=>o(s).pageSize=l),onPagination:w},null,8,["total","page","limit"]),[[O,o(B)>0]]),t(ce,{title:o(R),modelValue:o(y),"onUpdate:modelValue":e[10]||(e[10]=l=>W(y)?y.value=l:null),width:"780px","append-to-body":""},{footer:n(()=>[X("div",ke,[t(c,{type:"primary",onClick:ae},{default:n(()=>e[18]||(e[18]=[p("确 定")])),_:1,__:[18]}),t(c,{onClick:te},{default:n(()=>e[19]||(e[19]=[p("取 消")])),_:1,__:[19]})])]),default:n(()=>[t(A,{ref:"noticeRef",model:o(i),rules:o(ee),"label-width":"80px"},{default:n(()=>[t(G,null,{default:n(()=>[t(V,{span:12},{default:n(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:n(()=>[t(T,{modelValue:o(i).noticeTitle,"onUpdate:modelValue":e[6]||(e[6]=l=>o(i).noticeTitle=l),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1})]),_:1}),t(V,{span:12},{default:n(()=>[t(g,{label:"公告类型",prop:"noticeType"},{default:n(()=>[t(j,{modelValue:o(i).noticeType,"onUpdate:modelValue":e[7]||(e[7]=l=>o(i).noticeType=l),placeholder:"请选择"},{default:n(()=>[(d(!0),N($,null,q(o(U),l=>(d(),f(Q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:24},{default:n(()=>[t(g,{label:"状态"},{default:n(()=>[t(re,{modelValue:o(i).status,"onUpdate:modelValue":e[8]||(e[8]=l=>o(i).status=l)},{default:n(()=>[(d(!0),N($,null,q(o(P),l=>(d(),f(de,{key:l.value,value:l.value},{default:n(()=>[p(Y(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:24},{default:n(()=>[t(g,{label:"内容"},{default:n(()=>[t(pe,{modelValue:o(i).noticeContent,"onUpdate:modelValue":e[9]||(e[9]=l=>o(i).noticeContent=l),"min-height":192},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ue as default};
