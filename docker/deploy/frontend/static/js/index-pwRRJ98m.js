import{$ as o,_ as Ae,r as c,e as u,c as x,o as i,f as s,h as m,i as e,l,C as W,J as j,K,L as Le,t as Q,z as Oe,d as ye,A as be,I as ke,F as te,G as U,k as I,H as we,m as xe,j as r,n as a,a0 as oe,D as he}from"./index-CdiCNU81.js";import{l as le,g as Ce,d as Ne,u as Ue,a as Se}from"./menu-BcsgTeEp.js";let $=[];const qe=Object.assign({"../../assets/icons/svg/404.svg":()=>o(()=>import("./404-Dy3nURRX.js"),[]),"../../assets/icons/svg/bug.svg":()=>o(()=>import("./bug-10dePVta.js"),[]),"../../assets/icons/svg/build.svg":()=>o(()=>import("./build-2jMyI6eP.js"),[]),"../../assets/icons/svg/button.svg":()=>o(()=>import("./button-BlSCM_GH.js"),[]),"../../assets/icons/svg/cascader.svg":()=>o(()=>import("./cascader-CXIOcY1C.js"),[]),"../../assets/icons/svg/chart.svg":()=>o(()=>import("./chart-BsLMrzXU.js"),[]),"../../assets/icons/svg/checkbox.svg":()=>o(()=>import("./checkbox-Bpiun3bf.js"),[]),"../../assets/icons/svg/clipboard.svg":()=>o(()=>import("./clipboard-DaV3cn7f.js"),[]),"../../assets/icons/svg/code.svg":()=>o(()=>import("./code-DgJ8cT4a.js"),[]),"../../assets/icons/svg/color.svg":()=>o(()=>import("./color-y1Sshoou.js"),[]),"../../assets/icons/svg/component.svg":()=>o(()=>import("./component-Djp9s69L.js"),[]),"../../assets/icons/svg/dashboard.svg":()=>o(()=>import("./dashboard-Dy7qt_a2.js"),[]),"../../assets/icons/svg/date-range.svg":()=>o(()=>import("./date-range-B8MgYLb1.js"),[]),"../../assets/icons/svg/date.svg":()=>o(()=>import("./date-B1FSITvi.js"),[]),"../../assets/icons/svg/dict.svg":()=>o(()=>import("./dict-Bi_GqSXR.js"),[]),"../../assets/icons/svg/documentation.svg":()=>o(()=>import("./documentation-uH9BvL5U.js"),[]),"../../assets/icons/svg/download.svg":()=>o(()=>import("./download-DeIzgQWH.js"),[]),"../../assets/icons/svg/drag.svg":()=>o(()=>import("./drag-BG1_I1vT.js"),[]),"../../assets/icons/svg/druid.svg":()=>o(()=>import("./druid-BybW_S_B.js"),[]),"../../assets/icons/svg/edit.svg":()=>o(()=>import("./edit-D0DI9pAq.js"),[]),"../../assets/icons/svg/education.svg":()=>o(()=>import("./education-47KsSYIl.js"),[]),"../../assets/icons/svg/email.svg":()=>o(()=>import("./email-Dig28Vt2.js"),[]),"../../assets/icons/svg/enter.svg":()=>o(()=>import("./enter-KOZ0bgqJ.js"),[]),"../../assets/icons/svg/example.svg":()=>o(()=>import("./example-CnLLAFb9.js"),[]),"../../assets/icons/svg/excel.svg":()=>o(()=>import("./excel-D3hj5F35.js"),[]),"../../assets/icons/svg/exit-fullscreen.svg":()=>o(()=>import("./exit-fullscreen-dXhGKlQm.js"),[]),"../../assets/icons/svg/eye-open.svg":()=>o(()=>import("./eye-open-BxlshWqB.js"),[]),"../../assets/icons/svg/eye.svg":()=>o(()=>import("./eye-DqRz4sMZ.js"),[]),"../../assets/icons/svg/form.svg":()=>o(()=>import("./form-BDTA_i-I.js"),[]),"../../assets/icons/svg/fullscreen.svg":()=>o(()=>import("./fullscreen-0JHt5yWX.js"),[]),"../../assets/icons/svg/github.svg":()=>o(()=>import("./github-AJ0WQBa2.js"),[]),"../../assets/icons/svg/guide.svg":()=>o(()=>import("./guide-DZWUPi2j.js"),[]),"../../assets/icons/svg/icon.svg":()=>o(()=>import("./icon-BtMv6Od8.js"),[]),"../../assets/icons/svg/input.svg":()=>o(()=>import("./input-BJoPMnBW.js"),[]),"../../assets/icons/svg/international.svg":()=>o(()=>import("./international-CmzG1OHg.js"),[]),"../../assets/icons/svg/job.svg":()=>o(()=>import("./job-BcmuINx7.js"),[]),"../../assets/icons/svg/language.svg":()=>o(()=>import("./language-CaW1LMEk.js"),[]),"../../assets/icons/svg/link.svg":()=>o(()=>import("./link-C93f4PgI.js"),[]),"../../assets/icons/svg/list.svg":()=>o(()=>import("./list-C7O8B4zW.js"),[]),"../../assets/icons/svg/lock.svg":()=>o(()=>import("./lock-Bexeb9hp.js"),[]),"../../assets/icons/svg/log.svg":()=>o(()=>import("./log-CF2F-nSs.js"),[]),"../../assets/icons/svg/logininfor.svg":()=>o(()=>import("./logininfor-Bm9ZYYR7.js"),[]),"../../assets/icons/svg/message.svg":()=>o(()=>import("./message-UkR-VIBB.js"),[]),"../../assets/icons/svg/money.svg":()=>o(()=>import("./money-B1qqPuhn.js"),[]),"../../assets/icons/svg/monitor.svg":()=>o(()=>import("./monitor-gwnnVq4l.js"),[]),"../../assets/icons/svg/moon.svg":()=>o(()=>import("./moon-BOcjHwCq.js"),[]),"../../assets/icons/svg/more-up.svg":()=>o(()=>import("./more-up-u2qZwiNm.js"),[]),"../../assets/icons/svg/nested.svg":()=>o(()=>import("./nested-B4d5u3hW.js"),[]),"../../assets/icons/svg/number.svg":()=>o(()=>import("./number-D4hB_nHC.js"),[]),"../../assets/icons/svg/online.svg":()=>o(()=>import("./online-C2ZP8pdY.js"),[]),"../../assets/icons/svg/password.svg":()=>o(()=>import("./password-DfGvqQpB.js"),[]),"../../assets/icons/svg/pdf.svg":()=>o(()=>import("./pdf-CD9mOGjJ.js"),[]),"../../assets/icons/svg/people.svg":()=>o(()=>import("./people-CdGMHN63.js"),[]),"../../assets/icons/svg/peoples.svg":()=>o(()=>import("./peoples-BRYsIqmI.js"),[]),"../../assets/icons/svg/phone.svg":()=>o(()=>import("./phone-BpAUIz0g.js"),[]),"../../assets/icons/svg/post.svg":()=>o(()=>import("./post-DrLDyPY9.js"),[]),"../../assets/icons/svg/qq.svg":()=>o(()=>import("./qq-D8j4O83Y.js"),[]),"../../assets/icons/svg/question.svg":()=>o(()=>import("./question-CvYWQbyW.js"),[]),"../../assets/icons/svg/radio.svg":()=>o(()=>import("./radio-B0t9wPBQ.js"),[]),"../../assets/icons/svg/rate.svg":()=>o(()=>import("./rate-CgnHQvKS.js"),[]),"../../assets/icons/svg/redis-list.svg":()=>o(()=>import("./redis-list-BtKGPnqO.js"),[]),"../../assets/icons/svg/redis.svg":()=>o(()=>import("./redis-D4ECyT6a.js"),[]),"../../assets/icons/svg/row.svg":()=>o(()=>import("./row-CRXKIHjm.js"),[]),"../../assets/icons/svg/search.svg":()=>o(()=>import("./search-CUfclCsR.js"),[]),"../../assets/icons/svg/select.svg":()=>o(()=>import("./select-DhuHHMxz.js"),[]),"../../assets/icons/svg/server.svg":()=>o(()=>import("./server-unS7EyF7.js"),[]),"../../assets/icons/svg/shopping.svg":()=>o(()=>import("./shopping-CU1IRvxM.js"),[]),"../../assets/icons/svg/size.svg":()=>o(()=>import("./size-Cj9fB5Rp.js"),[]),"../../assets/icons/svg/skill.svg":()=>o(()=>import("./skill-B8f_I4m_.js"),[]),"../../assets/icons/svg/slider.svg":()=>o(()=>import("./slider-BGfehM6X.js"),[]),"../../assets/icons/svg/star.svg":()=>o(()=>import("./star-kST8a72V.js"),[]),"../../assets/icons/svg/sunny.svg":()=>o(()=>import("./sunny-DvkHW8g8.js"),[]),"../../assets/icons/svg/swagger.svg":()=>o(()=>import("./swagger-BHGXZ2Jt.js"),[]),"../../assets/icons/svg/switch.svg":()=>o(()=>import("./switch-CvaargRJ.js"),[]),"../../assets/icons/svg/system.svg":()=>o(()=>import("./system-DcNSH_Fq.js"),[]),"../../assets/icons/svg/tab.svg":()=>o(()=>import("./tab-nA3f0aBt.js"),[]),"../../assets/icons/svg/table.svg":()=>o(()=>import("./table-5PRh60AQ.js"),[]),"../../assets/icons/svg/textarea.svg":()=>o(()=>import("./textarea-CJWXlgbJ.js"),[]),"../../assets/icons/svg/theme.svg":()=>o(()=>import("./theme-CyGq941x.js"),[]),"../../assets/icons/svg/time-range.svg":()=>o(()=>import("./time-range-D3dxgtLj.js"),[]),"../../assets/icons/svg/time.svg":()=>o(()=>import("./time-BVERp0sU.js"),[]),"../../assets/icons/svg/tool.svg":()=>o(()=>import("./tool-D8kXk1l-.js"),[]),"../../assets/icons/svg/tree-table.svg":()=>o(()=>import("./tree-table-CnOS99I9.js"),[]),"../../assets/icons/svg/tree.svg":()=>o(()=>import("./tree-BCtS3oPD.js"),[]),"../../assets/icons/svg/upload.svg":()=>o(()=>import("./upload-BueI-Il1.js"),[]),"../../assets/icons/svg/user.svg":()=>o(()=>import("./user-DqMuW5cU.js"),[]),"../../assets/icons/svg/validCode.svg":()=>o(()=>import("./validCode-COB1iLxa.js"),[]),"../../assets/icons/svg/wechat.svg":()=>o(()=>import("./wechat-lmQOcPZu.js"),[]),"../../assets/icons/svg/zip.svg":()=>o(()=>import("./zip-DIOSZc69.js"),[]),"../../assets/icons/svg/密钥管理.svg":()=>o(()=>import("./密钥管理-S3GsXIbr.js"),[]),"../../assets/icons/svg/应用管理.svg":()=>o(()=>import("./应用管理-CFe-I94r.js"),[]),"../../assets/icons/svg/接口日志.svg":()=>o(()=>import("./接口日志-CvK71Wja.js"),[]),"../../assets/icons/svg/项目管理.svg":()=>o(()=>import("./项目管理-SBlLdOlm.js"),[])});for(const M in qe){const g=M.split("assets/icons/svg/")[1].split(".svg")[0];$.push(g)}const Fe={class:"icon-body"},$e={class:"icon-list"},Me={class:"list-container"},ze=["onClick"],Be={__name:"index",props:{activeIcon:{type:String}},emits:["selected"],setup(M,{expose:g,emit:G}){const E=c(""),w=c($),f=G;function h(){w.value=$,E.value&&(w.value=$.filter(A=>A.indexOf(E.value)!==-1))}function C(A){f("selected",A),document.body.click()}function S(){E.value="",w.value=$}return g({reset:S}),(A,L)=>{const q=u("el-input"),z=u("svg-icon");return i(),x("div",Fe,[s(q,{modelValue:l(E),"onUpdate:modelValue":L[0]||(L[0]=O=>W(E)?E.value=O:null),class:"icon-search",clearable:"",placeholder:"请输入图标名称",onClear:h,onInput:h},{suffix:e(()=>L[1]||(L[1]=[m("i",{class:"el-icon-search el-input__icon"},null,-1)])),_:1},8,["modelValue"]),m("div",$e,[m("div",Me,[(i(!0),x(j,null,K(l(w),(O,y)=>(i(),x("div",{class:"icon-item-wrapper",key:y,onClick:_=>C(O)},[m("div",{class:Le(["icon-item",{active:M.activeIcon===O}])},[s(z,{"icon-class":O,"class-name":"icon",style:{height:"25px",width:"16px"}},null,8,["icon-class"]),m("span",null,Q(O),1)],2)],8,ze))),128))])])])}}},je=Ae(Be,[["__scopeId","data-v-7ed7ecf0"]]),Ke={class:"app-container"},Qe={class:"dialog-footer"},Ge=Oe({name:"Menu"}),We=Object.assign(Ge,{setup(M){const{proxy:g}=ye(),{sys_show_hide:G,sys_normal_disable:E}=g.useDict("sys_show_hide","sys_normal_disable"),w=c([]),f=c(!1),h=c(!0),C=c(!0),S=c(""),A=c([]),L=c(!1),q=c(!0),z=c(null),O=be({form:{},queryParams:{menuName:void 0,visible:void 0},rules:{menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}],path:[{required:!0,message:"路由地址不能为空",trigger:"blur"}]}}),{queryParams:y,form:_,rules:ne}=ke(O);function N(){h.value=!0,le(y.value).then(d=>{w.value=g.handleTree(d.data,"menuId"),h.value=!1})}function X(){A.value=[],le().then(d=>{const t={menuId:0,menuName:"主类目",children:[]};t.children=g.handleTree(d.data,"menuId"),A.value.push(t)})}function _e(){f.value=!1,H()}function H(){_.value={menuId:void 0,parentId:0,menuName:void 0,icon:void 0,menuType:"M",orderNum:void 0,isFrame:"1",isCache:"0",visible:"0",status:"0"},g.resetForm("menuRef")}function ae(){z.value.reset()}function ie(d){_.value.icon=d}function J(){N()}function ue(){g.resetForm("queryRef"),J()}function Y(d){H(),X(),d!=null&&d.menuId?_.value.parentId=d.menuId:_.value.parentId=0,f.value=!0,S.value="添加菜单"}function re(){q.value=!1,L.value=!L.value,he(()=>{q.value=!0})}async function de(d){H(),await X(),Ce(d.menuId).then(t=>{_.value=t.data,f.value=!0,S.value="修改菜单"})}function ve(){g.$refs.menuRef.validate(d=>{d&&(_.value.menuId!=null?Ue(_.value).then(t=>{g.$modal.msgSuccess("修改成功"),f.value=!1,N()}):Se(_.value).then(t=>{g.$modal.msgSuccess("新增成功"),f.value=!1,N()}))})}function pe(d){g.$modal.confirm('是否确认删除名称为"'+d.menuName+'"的数据项?').then(function(){return Ne(d.menuId)}).then(()=>{N(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}return N(),(d,t)=>{const b=u("el-input"),v=u("el-form-item"),me=u("el-option"),ge=u("el-select"),T=u("el-button"),Z=u("el-form"),p=u("el-col"),ce=u("right-toolbar"),ee=u("el-row"),k=u("el-table-column"),Ee=u("dict-tag"),fe=u("el-table"),Ve=u("el-tree-select"),R=u("el-radio"),F=u("el-radio-group"),Ie=u("search"),V=u("el-icon"),Te=u("el-popover"),Re=u("el-input-number"),P=u("question-filled"),D=u("el-tooltip"),Pe=u("el-dialog"),B=te("hasPermi"),De=te("loading");return i(),x("div",Ke,[U(s(Z,{model:l(y),ref:"queryRef",inline:!0},{default:e(()=>[s(v,{label:"菜单名称",prop:"menuName"},{default:e(()=>[s(b,{modelValue:l(y).menuName,"onUpdate:modelValue":t[0]||(t[0]=n=>l(y).menuName=n),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"},onKeyup:xe(J,["enter"])},null,8,["modelValue"])]),_:1}),s(v,{label:"状态",prop:"status"},{default:e(()=>[s(ge,{modelValue:l(y).status,"onUpdate:modelValue":t[1]||(t[1]=n=>l(y).status=n),placeholder:"菜单状态",clearable:"",style:{width:"200px"}},{default:e(()=>[(i(!0),x(j,null,K(l(E),n=>(i(),r(me,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(v,null,{default:e(()=>[s(T,{type:"primary",icon:"Search",onClick:J},{default:e(()=>t[18]||(t[18]=[a("搜索")])),_:1,__:[18]}),s(T,{icon:"Refresh",onClick:ue},{default:e(()=>t[19]||(t[19]=[a("重置")])),_:1,__:[19]})]),_:1})]),_:1},8,["model"]),[[we,l(C)]]),s(ee,{gutter:10,class:"mb8"},{default:e(()=>[s(p,{span:1.5},{default:e(()=>[U((i(),r(T,{type:"primary",plain:"",icon:"Plus",onClick:Y},{default:e(()=>t[20]||(t[20]=[a("新增")])),_:1,__:[20]})),[[B,["system:menu:add"]]])]),_:1}),s(p,{span:1.5},{default:e(()=>[s(T,{type:"info",plain:"",icon:"Sort",onClick:re},{default:e(()=>t[21]||(t[21]=[a("展开/折叠")])),_:1,__:[21]})]),_:1}),s(ce,{showSearch:l(C),"onUpdate:showSearch":t[2]||(t[2]=n=>W(C)?C.value=n:null),onQueryTable:N},null,8,["showSearch"])]),_:1}),l(q)?U((i(),r(fe,{key:0,data:l(w),"row-key":"menuId","default-expand-all":l(L),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:e(()=>[s(k,{prop:"menuName",label:"菜单名称","show-overflow-tooltip":!0,width:"160"}),s(k,{prop:"icon",label:"图标",align:"center",width:"100"},{default:e(n=>[s(l(oe),{"icon-class":n.row.icon},null,8,["icon-class"])]),_:1}),s(k,{prop:"orderNum",label:"排序",width:"60"}),s(k,{prop:"perms",label:"权限标识","show-overflow-tooltip":!0}),s(k,{prop:"component",label:"组件路径","show-overflow-tooltip":!0}),s(k,{prop:"status",label:"状态",width:"80"},{default:e(n=>[s(Ee,{options:l(E),value:n.row.status},null,8,["options","value"])]),_:1}),s(k,{label:"创建时间",align:"center",width:"160",prop:"createTime"},{default:e(n=>[m("span",null,Q(d.parseTime(n.row.createTime)),1)]),_:1}),s(k,{label:"操作",align:"center",width:"210","class-name":"small-padding fixed-width"},{default:e(n=>[U((i(),r(T,{link:"",type:"primary",icon:"Edit",onClick:se=>de(n.row)},{default:e(()=>t[22]||(t[22]=[a("修改")])),_:2,__:[22]},1032,["onClick"])),[[B,["system:menu:edit"]]]),U((i(),r(T,{link:"",type:"primary",icon:"Plus",onClick:se=>Y(n.row)},{default:e(()=>t[23]||(t[23]=[a("新增")])),_:2,__:[23]},1032,["onClick"])),[[B,["system:menu:add"]]]),U((i(),r(T,{link:"",type:"primary",icon:"Delete",onClick:se=>pe(n.row)},{default:e(()=>t[24]||(t[24]=[a("删除")])),_:2,__:[24]},1032,["onClick"])),[[B,["system:menu:remove"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[De,l(h)]]):I("",!0),s(Pe,{title:l(S),modelValue:l(f),"onUpdate:modelValue":t[17]||(t[17]=n=>W(f)?f.value=n:null),width:"680px","append-to-body":""},{footer:e(()=>[m("div",Qe,[s(T,{type:"primary",onClick:ve},{default:e(()=>t[41]||(t[41]=[a("确 定")])),_:1,__:[41]}),s(T,{onClick:_e},{default:e(()=>t[42]||(t[42]=[a("取 消")])),_:1,__:[42]})])]),default:e(()=>[s(Z,{ref:"menuRef",model:l(_),rules:l(ne),"label-width":"100px"},{default:e(()=>[s(ee,null,{default:e(()=>[s(p,{span:24},{default:e(()=>[s(v,{label:"上级菜单"},{default:e(()=>[s(Ve,{modelValue:l(_).parentId,"onUpdate:modelValue":t[3]||(t[3]=n=>l(_).parentId=n),data:l(A),props:{value:"menuId",label:"menuName",children:"children"},"value-key":"menuId",placeholder:"选择上级菜单","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1}),s(p,{span:24},{default:e(()=>[s(v,{label:"菜单类型",prop:"menuType"},{default:e(()=>[s(F,{modelValue:l(_).menuType,"onUpdate:modelValue":t[4]||(t[4]=n=>l(_).menuType=n)},{default:e(()=>[s(R,{value:"M"},{default:e(()=>t[25]||(t[25]=[a("目录")])),_:1,__:[25]}),s(R,{value:"C"},{default:e(()=>t[26]||(t[26]=[a("菜单")])),_:1,__:[26]}),s(R,{value:"F"},{default:e(()=>t[27]||(t[27]=[a("按钮")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(_).menuType!="F"?(i(),r(p,{key:0,span:12},{default:e(()=>[s(v,{label:"菜单图标",prop:"icon"},{default:e(()=>[s(Te,{placement:"bottom-start",width:540,trigger:"click"},{reference:e(()=>[s(b,{modelValue:l(_).icon,"onUpdate:modelValue":t[5]||(t[5]=n=>l(_).icon=n),placeholder:"点击选择图标",onBlur:ae,readonly:""},{prefix:e(()=>[l(_).icon?(i(),r(l(oe),{key:0,"icon-class":l(_).icon,class:"el-input__icon",style:{height:"32px",width:"16px"}},null,8,["icon-class"])):(i(),r(V,{key:1,style:{height:"32px",width:"16px"}},{default:e(()=>[s(Ie)]),_:1}))]),_:1},8,["modelValue"])]),default:e(()=>[s(l(je),{ref_key:"iconSelectRef",ref:z,onSelected:ie,"active-icon":l(_).icon},null,8,["active-icon"])]),_:1})]),_:1})]),_:1})):I("",!0),s(p,{span:12},{default:e(()=>[s(v,{label:"显示排序",prop:"orderNum"},{default:e(()=>[s(Re,{modelValue:l(_).orderNum,"onUpdate:modelValue":t[6]||(t[6]=n=>l(_).orderNum=n),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),s(p,{span:12},{default:e(()=>[s(v,{label:"菜单名称",prop:"menuName"},{default:e(()=>[s(b,{modelValue:l(_).menuName,"onUpdate:modelValue":t[7]||(t[7]=n=>l(_).menuName=n),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(_).menuType=="C"?(i(),r(p,{key:1,span:12},{default:e(()=>[s(v,{prop:"routeName"},{label:e(()=>[m("span",null,[s(D,{content:"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[28]||(t[28]=a(" 路由名称 "))])]),default:e(()=>[s(b,{modelValue:l(_).routeName,"onUpdate:modelValue":t[8]||(t[8]=n=>l(_).routeName=n),placeholder:"请输入路由名称"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType!="F"?(i(),r(p,{key:2,span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[29]||(t[29]=a("是否外链 "))])]),default:e(()=>[s(F,{modelValue:l(_).isFrame,"onUpdate:modelValue":t[9]||(t[9]=n=>l(_).isFrame=n)},{default:e(()=>[s(R,{value:"0"},{default:e(()=>t[30]||(t[30]=[a("是")])),_:1,__:[30]}),s(R,{value:"1"},{default:e(()=>t[31]||(t[31]=[a("否")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType!="F"?(i(),r(p,{key:3,span:12},{default:e(()=>[s(v,{prop:"path"},{label:e(()=>[m("span",null,[s(D,{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[32]||(t[32]=a(" 路由地址 "))])]),default:e(()=>[s(b,{modelValue:l(_).path,"onUpdate:modelValue":t[10]||(t[10]=n=>l(_).path=n),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType=="C"?(i(),r(p,{key:4,span:12},{default:e(()=>[s(v,{prop:"component"},{label:e(()=>[m("span",null,[s(D,{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[33]||(t[33]=a(" 组件路径 "))])]),default:e(()=>[s(b,{modelValue:l(_).component,"onUpdate:modelValue":t[11]||(t[11]=n=>l(_).component=n),placeholder:"请输入组件路径"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType!="M"?(i(),r(p,{key:5,span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[34]||(t[34]=a(" 权限字符 "))])]),default:e(()=>[s(b,{modelValue:l(_).perms,"onUpdate:modelValue":t[12]||(t[12]=n=>l(_).perms=n),placeholder:"请输入权限标识",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType=="C"?(i(),r(p,{key:6,span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:'访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[35]||(t[35]=a(" 路由参数 "))])]),default:e(()=>[s(b,{modelValue:l(_).query,"onUpdate:modelValue":t[13]||(t[13]=n=>l(_).query=n),placeholder:"请输入路由参数",maxlength:"255"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType=="C"?(i(),r(p,{key:7,span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[36]||(t[36]=a(" 是否缓存 "))])]),default:e(()=>[s(F,{modelValue:l(_).isCache,"onUpdate:modelValue":t[14]||(t[14]=n=>l(_).isCache=n)},{default:e(()=>[s(R,{value:"0"},{default:e(()=>t[37]||(t[37]=[a("缓存")])),_:1,__:[37]}),s(R,{value:"1"},{default:e(()=>t[38]||(t[38]=[a("不缓存")])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),l(_).menuType!="F"?(i(),r(p,{key:8,span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[39]||(t[39]=a(" 显示状态 "))])]),default:e(()=>[s(F,{modelValue:l(_).visible,"onUpdate:modelValue":t[15]||(t[15]=n=>l(_).visible=n)},{default:e(()=>[(i(!0),x(j,null,K(l(G),n=>(i(),r(R,{key:n.value,value:n.value},{default:e(()=>[a(Q(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),s(p,{span:12},{default:e(()=>[s(v,null,{label:e(()=>[m("span",null,[s(D,{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),t[40]||(t[40]=a(" 菜单状态 "))])]),default:e(()=>[s(F,{modelValue:l(_).status,"onUpdate:modelValue":t[16]||(t[16]=n=>l(_).status=n)},{default:e(()=>[(i(!0),x(j,null,K(l(E),n=>(i(),r(R,{key:n.value,value:n.value},{default:e(()=>[a(Q(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{We as default};
