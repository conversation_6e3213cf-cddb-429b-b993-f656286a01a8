import{r as f,d as K,A as j,e as m,j as D,o as I,i as l,f as t,l as o,m as C,n as b,G as L,H as P,h as Q,C as A}from"./index-CdiCNU81.js";import{l as E,i as F}from"./gen-COW18MkV.js";const G={class:"dialog-footer"},O={__name:"importTable",emits:["ok"],setup(H,{expose:N,emit:V}){const _=f(0),s=f(!1),w=f([]),v=f([]),{proxy:p}=K(),a=j({pageNum:1,pageSize:10,tableName:void 0,tableComment:void 0}),k=V;function x(){c(),s.value=!0}function S(n){p.$refs.table.toggleRowSelection(n)}function h(n){w.value=n.map(e=>e.tableName)}function c(){E(a).then(n=>{v.value=n.rows,_.value=n.total})}function u(){a.pageNum=1,c()}function T(){p.resetForm("queryRef"),u()}function R(){const n=w.value.join(",");if(n==""){p.$modal.msgError("请选择要导入的表");return}F({tables:n}).then(e=>{p.$modal.msgSuccess(e.msg),e.code===200&&(s.value=!1,k("ok"))})}return N({show:x}),(n,e)=>{const y=m("el-input"),g=m("el-form-item"),d=m("el-button"),U=m("el-form"),r=m("el-table-column"),$=m("el-table"),q=m("pagination"),z=m("el-row"),B=m("el-dialog");return I(),D(B,{title:"导入表",modelValue:o(s),"onUpdate:modelValue":e[5]||(e[5]=i=>A(s)?s.value=i:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[Q("div",G,[t(d,{type:"primary",onClick:R},{default:l(()=>e[8]||(e[8]=[b("确 定")])),_:1,__:[8]}),t(d,{onClick:e[4]||(e[4]=i=>s.value=!1)},{default:l(()=>e[9]||(e[9]=[b("取 消")])),_:1,__:[9]})])]),default:l(()=>[t(U,{model:o(a),ref:"queryRef",inline:!0},{default:l(()=>[t(g,{label:"表名称",prop:"tableName"},{default:l(()=>[t(y,{modelValue:o(a).tableName,"onUpdate:modelValue":e[0]||(e[0]=i=>o(a).tableName=i),placeholder:"请输入表名称",clearable:"",style:{width:"180px"},onKeyup:C(u,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"表描述",prop:"tableComment"},{default:l(()=>[t(y,{modelValue:o(a).tableComment,"onUpdate:modelValue":e[1]||(e[1]=i=>o(a).tableComment=i),placeholder:"请输入表描述",clearable:"",style:{width:"180px"},onKeyup:C(u,["enter"])},null,8,["modelValue"])]),_:1}),t(g,null,{default:l(()=>[t(d,{type:"primary",icon:"Search",onClick:u},{default:l(()=>e[6]||(e[6]=[b("搜索")])),_:1,__:[6]}),t(d,{icon:"Refresh",onClick:T},{default:l(()=>e[7]||(e[7]=[b("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),t(z,null,{default:l(()=>[t($,{onRowClick:S,ref:"table",data:o(v),onSelectionChange:h,height:"260px"},{default:l(()=>[t(r,{type:"selection",width:"55"}),t(r,{prop:"tableName",label:"表名称","show-overflow-tooltip":!0}),t(r,{prop:"tableComment",label:"表描述","show-overflow-tooltip":!0}),t(r,{prop:"createTime",label:"创建时间"}),t(r,{prop:"updateTime",label:"更新时间"})]),_:1},8,["data"]),L(t(q,{total:o(_),page:o(a).pageNum,"onUpdate:page":e[2]||(e[2]=i=>o(a).pageNum=i),limit:o(a).pageSize,"onUpdate:limit":e[3]||(e[3]=i=>o(a).pageSize=i),onPagination:c},null,8,["total","page","limit"]),[[P,o(_)>0]])]),_:1})]),_:1},8,["modelValue"])}}};export{O as default};
