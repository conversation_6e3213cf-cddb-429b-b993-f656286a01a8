import{ac as D,ad as j,r as d,e as n,c as V,o as m,f as o,i as a,l as u,C as E,J,K,j as L,h as R,n as g}from"./index-CdiCNU81.js";const z={class:"dialog-footer"},G={__name:"TreeNodeDialog",props:{modelValue:{},modelModifiers:{}},emits:D(["confirm"],["update:modelValue"]),setup(k,{emit:y}){const r=j(k,"modelValue"),C=y,t=d({label:void 0,value:void 0}),x={label:[{required:!0,message:"请输入选项名",trigger:"blur"}],value:[{required:!0,message:"请输入选项值",trigger:"blur"}]},s=d("string"),N=d([{label:"字符串",value:"string"},{label:"数字",value:"number"}]),w=d(100),p=d();function B(){t.value={label:void 0,value:void 0}}function i(){r.value=!1}function F(){p.value.validate(f=>{f&&(s.value==="number"&&(t.value.value=parseFloat(t.value.value)),t.value.id=w.value++,C("commit",t.value),i())})}return(f,e)=>{const _=n("el-input"),c=n("el-form-item"),v=n("el-col"),T=n("el-option"),U=n("el-select"),M=n("el-form"),b=n("el-button"),O=n("el-dialog");return m(),V("div",null,[o(O,{title:"添加选项",modelValue:r.value,"onUpdate:modelValue":e[3]||(e[3]=l=>r.value=l),width:"800px","close-on-click-modal":!1,"modal-append-to-body":!1,onOpen:B,onClose:i},{footer:a(()=>[R("div",z,[o(b,{type:"primary",onClick:F},{default:a(()=>e[4]||(e[4]=[g("确 定")])),_:1,__:[4]}),o(b,{onClick:i},{default:a(()=>e[5]||(e[5]=[g("取 消")])),_:1,__:[5]})])]),default:a(()=>[o(M,{ref_key:"treeNodeForm",ref:p,model:u(t),rules:x,"label-width":"100px"},{default:a(()=>[o(v,{span:24},{default:a(()=>[o(c,{label:"选项名",prop:"label"},{default:a(()=>[o(_,{modelValue:u(t).label,"onUpdate:modelValue":e[0]||(e[0]=l=>u(t).label=l),placeholder:"请输入选项名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),o(v,{span:24},{default:a(()=>[o(c,{label:"选项值",prop:"value"},{default:a(()=>[o(_,{modelValue:u(t).value,"onUpdate:modelValue":e[2]||(e[2]=l=>u(t).value=l),placeholder:"请输入选项值",clearable:""},{append:a(()=>[o(U,{modelValue:u(s),"onUpdate:modelValue":e[1]||(e[1]=l=>E(s)?s.value=l:null),style:{width:"100px"}},{default:a(()=>[(m(!0),V(J,null,K(u(N),(l,q)=>(m(),L(T,{key:q,label:l.label,value:l.value,disabled:l.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}};export{G as default};
