import{S as B,z as me,d as fe,r as _,A as _e,I as ce,e as p,F as Q,c as k,o as m,G as w,f as e,H as A,l as t,i as l,m as $,J as E,K as G,j as b,C as q,n as d,h as P,t as i,k as J}from"./index-CdiCNU81.js";function ge(C){return B({url:"/monitor/operlog/list",method:"get",params:C})}function be(C){return B({url:"/monitor/operlog/"+C,method:"delete"})}function ve(){return B({url:"/monitor/operlog/clean",method:"delete"})}const ye={class:"app-container"},we={key:0},he={key:1},Ve={class:"dialog-footer"},ke=me({name:"Operlog"}),Te=Object.assign(ke,{setup(C){const{proxy:c}=fe(),{sys_oper_type:N,sys_common_status:K}=c.useDict("sys_oper_type","sys_common_status"),L=_([]),h=_(!1),I=_(!0),x=_(!0),z=_([]);_(!0);const F=_(!0),U=_(0);_("");const V=_([]),R=_({prop:"operTime",order:"descending"}),W=_e({form:{},queryParams:{pageNum:1,pageSize:10,operIp:void 0,title:void 0,operName:void 0,businessType:void 0,status:void 0}}),{queryParams:n,form:r}=ce(W);function v(){I.value=!0,ge(c.addDateRange(n.value,V.value)).then(u=>{L.value=u.rows,U.value=u.total,I.value=!1})}function X(u,o){return c.selectDictLabel(N.value,u.businessType)}function T(){n.value.pageNum=1,v()}function Z(){V.value=[],c.resetForm("queryRef"),n.value.pageNum=1,c.$refs.operlogRef.sort(R.value.prop,R.value.order)}function ee(u){z.value=u.map(o=>o.operId),F.value=!u.length}function le(u,o,S){n.value.orderByColumn=u.prop,n.value.isAsc=u.order,v()}function te(u){h.value=!0,r.value=u}function oe(u){const o=u.operId||z.value;c.$modal.confirm('是否确认删除日志编号为"'+o+'"的数据项?').then(function(){return be(o)}).then(()=>{v(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ae(){c.$modal.confirm("是否确认清空所有操作日志数据项?").then(function(){return ve()}).then(()=>{v(),c.$modal.msgSuccess("清空成功")}).catch(()=>{})}function ne(){c.download("monitor/operlog/export",{...n.value},`config_${new Date().getTime()}.xlsx`)}return v(),(u,o)=>{const S=p("el-input"),s=p("el-form-item"),M=p("el-option"),O=p("el-select"),re=p("el-date-picker"),y=p("el-button"),Y=p("el-form"),f=p("el-col"),se=p("right-toolbar"),j=p("el-row"),g=p("el-table-column"),H=p("dict-tag"),ue=p("el-table"),de=p("pagination"),pe=p("el-dialog"),D=Q("hasPermi"),ie=Q("loading");return m(),k("div",ye,[w(e(Y,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(s,{label:"操作地址",prop:"operIp"},{default:l(()=>[e(S,{modelValue:t(n).operIp,"onUpdate:modelValue":o[0]||(o[0]=a=>t(n).operIp=a),placeholder:"请输入操作地址",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"系统模块",prop:"title"},{default:l(()=>[e(S,{modelValue:t(n).title,"onUpdate:modelValue":o[1]||(o[1]=a=>t(n).title=a),placeholder:"请输入系统模块",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"操作人员",prop:"operName"},{default:l(()=>[e(S,{modelValue:t(n).operName,"onUpdate:modelValue":o[2]||(o[2]=a=>t(n).operName=a),placeholder:"请输入操作人员",clearable:"",style:{width:"240px"},onKeyup:$(T,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"类型",prop:"businessType"},{default:l(()=>[e(O,{modelValue:t(n).businessType,"onUpdate:modelValue":o[3]||(o[3]=a=>t(n).businessType=a),placeholder:"操作类型",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(E,null,G(t(N),a=>(m(),b(M,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"状态",prop:"status"},{default:l(()=>[e(O,{modelValue:t(n).status,"onUpdate:modelValue":o[4]||(o[4]=a=>t(n).status=a),placeholder:"操作状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(E,null,G(t(K),a=>(m(),b(M,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"操作时间",style:{width:"308px"}},{default:l(()=>[e(re,{modelValue:t(V),"onUpdate:modelValue":o[5]||(o[5]=a=>q(V)?V.value=a:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),e(s,null,{default:l(()=>[e(y,{type:"primary",icon:"Search",onClick:T},{default:l(()=>o[11]||(o[11]=[d("搜索")])),_:1,__:[11]}),e(y,{icon:"Refresh",onClick:Z},{default:l(()=>o[12]||(o[12]=[d("重置")])),_:1,__:[12]})]),_:1})]),_:1},8,["model"]),[[A,t(x)]]),e(j,{gutter:10,class:"mb8"},{default:l(()=>[e(f,{span:1.5},{default:l(()=>[w((m(),b(y,{type:"danger",plain:"",icon:"Delete",disabled:t(F),onClick:oe},{default:l(()=>o[13]||(o[13]=[d("删除")])),_:1,__:[13]},8,["disabled"])),[[D,["monitor:operlog:remove"]]])]),_:1}),e(f,{span:1.5},{default:l(()=>[w((m(),b(y,{type:"danger",plain:"",icon:"Delete",onClick:ae},{default:l(()=>o[14]||(o[14]=[d("清空")])),_:1,__:[14]})),[[D,["monitor:operlog:remove"]]])]),_:1}),e(f,{span:1.5},{default:l(()=>[w((m(),b(y,{type:"warning",plain:"",icon:"Download",onClick:ne},{default:l(()=>o[15]||(o[15]=[d("导出")])),_:1,__:[15]})),[[D,["monitor:operlog:export"]]])]),_:1}),e(se,{showSearch:t(x),"onUpdate:showSearch":o[6]||(o[6]=a=>q(x)?x.value=a:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),w((m(),b(ue,{ref:"operlogRef",data:t(L),onSelectionChange:ee,"default-sort":t(R),onSortChange:le},{default:l(()=>[e(g,{type:"selection",width:"50",align:"center"}),e(g,{label:"日志编号",align:"center",prop:"operId"}),e(g,{label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0}),e(g,{label:"操作类型",align:"center",prop:"businessType"},{default:l(a=>[e(H,{options:t(N),value:a.row.businessType},null,8,["options","value"])]),_:1}),e(g,{label:"操作人员",align:"center",width:"110",prop:"operName","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作地址",align:"center",prop:"operIp",width:"130","show-overflow-tooltip":!0}),e(g,{label:"操作状态",align:"center",prop:"status"},{default:l(a=>[e(H,{options:t(K),value:a.row.status},null,8,["options","value"])]),_:1}),e(g,{label:"操作日期",align:"center",prop:"operTime",width:"180",sortable:"custom","sort-orders":["descending","ascending"]},{default:l(a=>[P("span",null,i(u.parseTime(a.row.operTime)),1)]),_:1}),e(g,{label:"消耗时间",align:"center",prop:"costTime",width:"110","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]},{default:l(a=>[P("span",null,i(a.row.costTime)+"毫秒",1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[w((m(),b(y,{link:"",type:"primary",icon:"View",onClick:Ce=>te(a.row,a.index)},{default:l(()=>o[16]||(o[16]=[d("详细")])),_:2,__:[16]},1032,["onClick"])),[[D,["monitor:operlog:query"]]])]),_:1})]),_:1},8,["data","default-sort"])),[[ie,t(I)]]),w(e(de,{total:t(U),page:t(n).pageNum,"onUpdate:page":o[7]||(o[7]=a=>t(n).pageNum=a),limit:t(n).pageSize,"onUpdate:limit":o[8]||(o[8]=a=>t(n).pageSize=a),onPagination:v},null,8,["total","page","limit"]),[[A,t(U)>0]]),e(pe,{title:"操作日志详细",modelValue:t(h),"onUpdate:modelValue":o[10]||(o[10]=a=>q(h)?h.value=a:null),width:"800px","append-to-body":""},{footer:l(()=>[P("div",Ve,[e(y,{onClick:o[9]||(o[9]=a=>h.value=!1)},{default:l(()=>o[17]||(o[17]=[d("关 闭")])),_:1,__:[17]})])]),default:l(()=>[e(Y,{model:t(r),"label-width":"100px"},{default:l(()=>[e(j,null,{default:l(()=>[e(f,{span:12},{default:l(()=>[e(s,{label:"操作模块："},{default:l(()=>[d(i(t(r).title)+" / "+i(X(t(r))),1)]),_:1}),e(s,{label:"登录信息："},{default:l(()=>[d(i(t(r).operName)+" / "+i(t(r).operIp)+" / "+i(t(r).operLocation),1)]),_:1})]),_:1}),e(f,{span:12},{default:l(()=>[e(s,{label:"请求地址："},{default:l(()=>[d(i(t(r).operUrl),1)]),_:1}),e(s,{label:"请求方式："},{default:l(()=>[d(i(t(r).requestMethod),1)]),_:1})]),_:1}),e(f,{span:24},{default:l(()=>[e(s,{label:"操作方法："},{default:l(()=>[d(i(t(r).method),1)]),_:1})]),_:1}),e(f,{span:24},{default:l(()=>[e(s,{label:"请求参数："},{default:l(()=>[d(i(t(r).operParam),1)]),_:1})]),_:1}),e(f,{span:24},{default:l(()=>[e(s,{label:"返回参数："},{default:l(()=>[d(i(t(r).jsonResult),1)]),_:1})]),_:1}),e(f,{span:8},{default:l(()=>[e(s,{label:"操作状态："},{default:l(()=>[t(r).status===0?(m(),k("div",we,"正常")):t(r).status===1?(m(),k("div",he,"失败")):J("",!0)]),_:1})]),_:1}),e(f,{span:8},{default:l(()=>[e(s,{label:"消耗时间："},{default:l(()=>[d(i(t(r).costTime)+"毫秒",1)]),_:1})]),_:1}),e(f,{span:8},{default:l(()=>[e(s,{label:"操作时间："},{default:l(()=>[d(i(u.parseTime(t(r).operTime)),1)]),_:1})]),_:1}),e(f,{span:24},{default:l(()=>[t(r).status===1?(m(),b(s,{key:0,label:"异常信息："},{default:l(()=>[d(i(t(r).errorMsg),1)]),_:1})):J("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Te as default};
