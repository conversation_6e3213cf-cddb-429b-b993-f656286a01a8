import{_ as p,d as f,r as m,e as n,c as g,o as k,f as o,i as s,n as a,h as e,l as x}from"./index-CdiCNU81.js";const h="/static/gif/401-HGF6Q5qM.gif",b={class:"errPage-container"},w={class:"list-unstyled"},y={class:"link-type"},B=["src"],C={__name:"401",setup(G){let{proxy:r}=f();const _=m(h+"?"+ +new Date);function c(){r.$route.query.noGoBack?r.$router.push({path:"/"}):r.$router.go(-1)}return(v,t)=>{const i=n("el-button"),u=n("router-link"),l=n("el-col"),d=n("el-row");return k(),g("div",b,[o(i,{icon:"arrow-left",class:"pan-back-btn",onClick:c},{default:s(()=>t[0]||(t[0]=[a(" 返回 ")])),_:1,__:[0]}),o(d,null,{default:s(()=>[o(l,{span:12},{default:s(()=>[t[2]||(t[2]=e("h1",{class:"text-jumbo text-ginormous"}," 401错误! ",-1)),t[3]||(t[3]=e("h2",null,"您没有访问权限！",-1)),t[4]||(t[4]=e("h6",null,"对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面",-1)),e("ul",w,[e("li",y,[o(u,{to:"/"},{default:s(()=>t[1]||(t[1]=[a(" 回首页 ")])),_:1,__:[1]})])])]),_:1,__:[2,3,4]}),o(l,{span:12},{default:s(()=>[e("img",{src:x(_),width:"313",height:"428",alt:"Girl has dropped her ice cream."},null,8,B)]),_:1})]),_:1})])}}},N=p(C,[["__scopeId","data-v-7afd82d1"]]);export{N as default};
