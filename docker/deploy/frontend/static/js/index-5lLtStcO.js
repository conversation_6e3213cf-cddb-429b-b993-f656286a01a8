import{z as ue,d as ie,r as c,A as pe,I as de,e as r,F as O,c as I,o as f,G as S,f as e,H as z,i as l,m as K,l as a,n as i,j as R,h as V,t as p}from"./index-CdiCNU81.js";import{e as me,f as ce}from"./openapi-BtKbeQMh.js";const _e={class:"app-container"},fe={key:0},ge={key:1},ye={key:1},be={class:"dialog-footer"},ve=ue({name:"OpenApiAccessLog"}),Ie=Object.assign(ve,{setup(we){const{proxy:q}=ie(),x=c([]),h=c(!1),U=c(!0),k=c(!0),D=c([]),B=c(!0),C=c(0),P=c([]),H=pe({form:{},queryParams:{pageNum:1,pageSize:10,apiKey:null,requestUri:null,clientIp:null,responseStatus:null,beginTime:null,endTime:null}}),{queryParams:s,form:u}=de(H);function Y(o){o&&o.length===2?(s.value.beginTime=o[0],s.value.endTime=o[1]):(s.value.beginTime=null,s.value.endTime=null)}function y(){U.value=!0,me(s.value).then(o=>{x.value=o.rows,C.value=o.total,U.value=!1})}function $(){h.value=!1,L()}function L(){u.value={logId:null,apiKey:null,requestUri:null,requestMethod:null,clientIp:null,userAgent:null,responseStatus:null,responseTime:null,requestTime:null,errorMessage:null}}function b(){s.value.pageNum=1,y()}function Q(){P.value=[],s.value.beginTime=null,s.value.endTime=null,q.resetForm("queryRef"),b()}function j(o){D.value=o.map(t=>t.logId),B.value=!o.length}function F(o){L(),u.value=o,h.value=!0}function G(o){const t=o.logId||D.value;q.$modal.confirm('是否确认删除访问日志编号为"'+t+'"的数据项？').then(function(){return ce(t)}).then(()=>{y(),q.$modal.msgSuccess("删除成功")}).catch(()=>{})}function N(o){return{GET:"info",POST:"success",PUT:"warning",DELETE:"danger",PATCH:"primary"}[o]||"info"}function E(o){return o>=200&&o<300?"success":o>=400&&o<500?"warning":o>=500?"danger":"info"}return y(),(o,t)=>{const A=r("el-input"),g=r("el-form-item"),_=r("el-col"),v=r("el-option"),J=r("el-select"),M=r("el-row"),W=r("el-date-picker"),w=r("el-button"),X=r("el-form"),Z=r("right-toolbar"),d=r("el-table-column"),T=r("el-tag"),ee=r("el-table"),le=r("pagination"),m=r("el-descriptions-item"),te=r("el-text"),ne=r("el-descriptions"),ae=r("el-dialog"),oe=O("hasPermi"),se=O("loading");return f(),I("div",_e,[S(e(X,{model:a(s),ref:"queryRef","label-width":"80px"},{default:l(()=>[e(M,{gutter:20},{default:l(()=>[e(_,{span:6},{default:l(()=>[e(g,{label:"API密钥",prop:"apiKey"},{default:l(()=>[e(A,{modelValue:a(s).apiKey,"onUpdate:modelValue":t[0]||(t[0]=n=>a(s).apiKey=n),placeholder:"请输入API密钥",clearable:"",onKeyup:K(b,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:l(()=>[e(g,{label:"请求URI",prop:"requestUri"},{default:l(()=>[e(A,{modelValue:a(s).requestUri,"onUpdate:modelValue":t[1]||(t[1]=n=>a(s).requestUri=n),placeholder:"请输入请求URI",clearable:"",onKeyup:K(b,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:l(()=>[e(g,{label:"客户端IP",prop:"clientIp"},{default:l(()=>[e(A,{modelValue:a(s).clientIp,"onUpdate:modelValue":t[2]||(t[2]=n=>a(s).clientIp=n),placeholder:"请输入客户端IP",clearable:"",onKeyup:K(b,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:l(()=>[e(g,{label:"响应状态",prop:"responseStatus"},{default:l(()=>[e(J,{modelValue:a(s).responseStatus,"onUpdate:modelValue":t[3]||(t[3]=n=>a(s).responseStatus=n),placeholder:"请选择响应状态",clearable:"",style:{width:"100%"}},{default:l(()=>[e(v,{label:"成功 (200)",value:"200"}),e(v,{label:"未授权 (401)",value:"401"}),e(v,{label:"禁止访问 (403)",value:"403"}),e(v,{label:"限流 (429)",value:"429"}),e(v,{label:"服务器错误 (500)",value:"500"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(M,{gutter:20},{default:l(()=>[e(_,{span:6},{default:l(()=>[e(g,{label:"请求时间",prop:"dateRange"},{default:l(()=>[e(W,{modelValue:P.value,"onUpdate:modelValue":t[4]||(t[4]=n=>P.value=n),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss",onChange:Y},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12}),e(_,{span:6},{default:l(()=>[e(g,{style:{"margin-right":"5px"}},{default:l(()=>[e(w,{type:"primary",icon:"Search",onClick:b},{default:l(()=>t[9]||(t[9]=[i("搜索")])),_:1,__:[9]}),e(w,{icon:"Refresh",onClick:Q},{default:l(()=>t[10]||(t[10]=[i("重置")])),_:1,__:[10]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),[[z,k.value]]),e(M,{gutter:10,class:"mb8"},{default:l(()=>[e(Z,{showSearch:k.value,"onUpdate:showSearch":t[5]||(t[5]=n=>k.value=n),onQueryTable:y},null,8,["showSearch"])]),_:1}),S((f(),R(ee,{data:x.value,onSelectionChange:j},{default:l(()=>[e(d,{type:"selection",width:"55",align:"center"}),e(d,{label:"序号",type:"index",width:"50",align:"center"}),e(d,{label:"API密钥",align:"center",prop:"apiKey","show-overflow-tooltip":"",width:"200"},{default:l(n=>[V("span",null,p(n.row.apiKey?n.row.apiKey.substring(0,16)+"***":"-"),1)]),_:1}),e(d,{label:"请求URI",align:"center",prop:"requestUri","show-overflow-tooltip":""}),e(d,{label:"请求方法",align:"center",prop:"requestMethod",width:"100"},{default:l(n=>[e(T,{type:N(n.row.requestMethod)},{default:l(()=>[i(p(n.row.requestMethod),1)]),_:2},1032,["type"])]),_:1}),e(d,{label:"客户端IP",align:"center",prop:"clientIp",width:"140"}),e(d,{label:"响应状态",align:"center",prop:"responseStatus",width:"100"},{default:l(n=>[e(T,{type:E(n.row.responseStatus)},{default:l(()=>[i(p(n.row.responseStatus),1)]),_:2},1032,["type"])]),_:1}),e(d,{label:"响应时间",align:"center",prop:"responseTime",width:"100"},{default:l(n=>[V("span",null,p(n.row.responseTime)+"ms",1)]),_:1}),e(d,{label:"错误信息",align:"center",prop:"errorMessage","show-overflow-tooltip":""}),e(d,{label:"用户代理",align:"center",prop:"userAgent","show-overflow-tooltip":""}),e(d,{label:"请求时间",align:"center",prop:"requestTime",width:"160"},{default:l(n=>[V("span",null,p(o.parseTime(n.row.requestTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(d,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"160"},{default:l(n=>[e(w,{link:"",type:"primary",icon:"View",onClick:re=>F(n.row)},{default:l(()=>t[11]||(t[11]=[i("详情")])),_:2,__:[11]},1032,["onClick"]),S((f(),R(w,{link:"",type:"primary",icon:"Delete",onClick:re=>G(n.row)},{default:l(()=>t[12]||(t[12]=[i("删除")])),_:2,__:[12]},1032,["onClick"])),[[oe,["system:openapi:log:remove"]]])]),_:1})]),_:1},8,["data"])),[[se,U.value]]),S(e(le,{total:C.value,page:a(s).pageNum,"onUpdate:page":t[6]||(t[6]=n=>a(s).pageNum=n),limit:a(s).pageSize,"onUpdate:limit":t[7]||(t[7]=n=>a(s).pageSize=n),onPagination:y},null,8,["total","page","limit"]),[[z,C.value>0]]),e(ae,{title:"访问日志详情",modelValue:h.value,"onUpdate:modelValue":t[8]||(t[8]=n=>h.value=n),width:"800px","append-to-body":""},{footer:l(()=>[V("div",be,[e(w,{onClick:$},{default:l(()=>t[13]||(t[13]=[i("关 闭")])),_:1,__:[13]})])]),default:l(()=>[e(ne,{column:2,border:""},{default:l(()=>[e(m,{label:"日志ID"},{default:l(()=>[i(p(a(u).logId),1)]),_:1}),e(m,{label:"API密钥"},{default:l(()=>[a(u).apiKey?(f(),I("span",fe,p(a(u).apiKey.substring(0,16))+"***",1)):(f(),I("span",ge,"-"))]),_:1}),e(m,{label:"请求URI"},{default:l(()=>[i(p(a(u).requestUri),1)]),_:1}),e(m,{label:"请求方法"},{default:l(()=>[e(T,{type:N(a(u).requestMethod)},{default:l(()=>[i(p(a(u).requestMethod),1)]),_:1},8,["type"])]),_:1}),e(m,{label:"客户端IP"},{default:l(()=>[i(p(a(u).clientIp),1)]),_:1}),e(m,{label:"响应状态"},{default:l(()=>[e(T,{type:E(a(u).responseStatus)},{default:l(()=>[i(p(a(u).responseStatus),1)]),_:1},8,["type"])]),_:1}),e(m,{label:"响应时间"},{default:l(()=>[i(p(a(u).responseTime)+"ms",1)]),_:1}),e(m,{label:"请求时间"},{default:l(()=>[i(p(o.parseTime(a(u).requestTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(m,{label:"用户代理",span:2},{default:l(()=>[i(p(a(u).userAgent||"-"),1)]),_:1}),e(m,{label:"错误信息",span:2},{default:l(()=>[a(u).errorMessage?(f(),R(te,{key:0,type:"danger"},{default:l(()=>[i(p(a(u).errorMessage),1)]),_:1})):(f(),I("span",ye,"-"))]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}});export{Ie as default};
