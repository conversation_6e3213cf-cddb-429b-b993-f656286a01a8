import{c as C}from"./gen-COW18MkV.js";import{r as i,d as y,e as s,j as w,o as T,i as n,h as p,f as r,C as m,l as f,n as _}from"./index-CdiCNU81.js";const B={class:"dialog-footer"},U={__name:"createTable",emits:["ok"],setup(N,{expose:c,emit:v}){const o=i(!1),t=i(""),{proxy:u}=y(),V=v;function x(){o.value=!0}function b(){if(t.value===""){u.$modal.msgError("请输入建表语句");return}C({sql:t.value}).then(a=>{u.$modal.msgSuccess(a.msg),a.code===200&&(o.value=!1,V("ok"))})}return c({show:x}),(a,e)=>{const g=s("el-input"),d=s("el-button"),k=s("el-dialog");return T(),w(k,{title:"创建表",modelValue:f(o),"onUpdate:modelValue":e[2]||(e[2]=l=>m(o)?o.value=l:null),width:"800px",top:"5vh","append-to-body":""},{footer:n(()=>[p("div",B,[r(d,{type:"primary",onClick:b},{default:n(()=>e[3]||(e[3]=[_("确 定")])),_:1,__:[3]}),r(d,{onClick:e[1]||(e[1]=l=>o.value=!1)},{default:n(()=>e[4]||(e[4]=[_("取 消")])),_:1,__:[4]})])]),default:n(()=>[e[5]||(e[5]=p("span",null,"创建表语句(支持多个建表语句)：",-1)),r(g,{type:"textarea",rows:10,placeholder:"请输入文本",modelValue:f(t),"onUpdate:modelValue":e[0]||(e[0]=l=>m(t)?t.value=l:null)},null,8,["modelValue"])]),_:1,__:[5]},8,["modelValue"])}}};export{U as default};
