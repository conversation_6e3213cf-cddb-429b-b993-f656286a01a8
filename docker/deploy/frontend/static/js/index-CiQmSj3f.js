import{S as N,r as M,d as T,e as r,c,o as i,f as o,i as _,h as l,k as d,l as t,t as a,n as m,L as b,J as B,K as J}from"./index-CdiCNU81.js";function V(){return N({url:"/monitor/server",method:"get"})}const L={class:"app-container"},G={class:"el-table el-table--enable-row-hover el-table--medium"},I={cellspacing:"0",style:{width:"100%"}},S={class:"el-table__cell is-leaf"},A={key:0,class:"cell"},D={class:"el-table__cell is-leaf"},P={key:0,class:"cell"},$={class:"el-table__cell is-leaf"},z={key:0,class:"cell"},E={class:"el-table__cell is-leaf"},K={key:0,class:"cell"},U={class:"el-table el-table--enable-row-hover el-table--medium"},q={cellspacing:"0",style:{width:"100%"}},H={class:"el-table__cell is-leaf"},O={key:0,class:"cell"},Q={class:"el-table__cell is-leaf"},R={key:0,class:"cell"},W={class:"el-table__cell is-leaf"},X={key:0,class:"cell"},Y={class:"el-table__cell is-leaf"},Z={key:0,class:"cell"},F={class:"el-table__cell is-leaf"},ll={key:0,class:"cell"},el={class:"el-table__cell is-leaf"},sl={key:0,class:"cell"},tl={class:"el-table__cell is-leaf"},al={class:"el-table__cell is-leaf"},cl={class:"el-table el-table--enable-row-hover el-table--medium"},il={cellspacing:"0",style:{width:"100%"}},dl={class:"el-table__cell is-leaf"},ol={key:0,class:"cell"},_l={class:"el-table__cell is-leaf"},nl={key:0,class:"cell"},rl={class:"el-table__cell is-leaf"},ul={key:0,class:"cell"},vl={class:"el-table__cell is-leaf"},ml={key:0,class:"cell"},bl={class:"el-table el-table--enable-row-hover el-table--medium"},fl={cellspacing:"0",style:{width:"100%","table-layout":"fixed"}},pl={class:"el-table__cell is-leaf"},hl={key:0,class:"cell"},yl={class:"el-table__cell is-leaf"},gl={key:0,class:"cell"},kl={class:"el-table__cell is-leaf"},jl={key:0,class:"cell"},wl={class:"el-table__cell is-leaf"},xl={key:0,class:"cell"},Cl={colspan:"3",class:"el-table__cell is-leaf"},Nl={key:0,class:"cell"},Ml={colspan:"3",class:"el-table__cell is-leaf"},Tl={key:0,class:"cell"},Bl={colspan:"3",class:"el-table__cell is-leaf"},Jl={key:0,class:"cell"},Vl={class:"el-table el-table--enable-row-hover el-table--medium"},Ll={cellspacing:"0",style:{width:"100%"}},Gl={key:0},Il={class:"el-table__cell is-leaf"},Sl={class:"cell"},Al={class:"el-table__cell is-leaf"},Dl={class:"cell"},Pl={class:"el-table__cell is-leaf"},$l={class:"cell"},zl={class:"el-table__cell is-leaf"},El={class:"cell"},Kl={class:"el-table__cell is-leaf"},Ul={class:"cell"},ql={class:"el-table__cell is-leaf"},Hl={class:"cell"},Ol={class:"el-table__cell is-leaf"},Wl={__name:"index",setup(Ql){const s=M([]),{proxy:f}=T();function h(){f.$modal.loading("正在加载服务监控数据，请稍候！"),V().then(p=>{s.value=p.data,f.$modal.closeLoading()})}return h(),(p,e)=>{const y=r("Cpu"),u=r("el-card"),v=r("el-col"),g=r("Tickets"),k=r("Monitor"),j=r("CoffeeCup"),w=r("MessageBox"),x=r("el-row");return i(),c("div",L,[o(x,{gutter:10},{default:_(()=>[o(v,{span:12,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(y,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[0]||(e[0]=m()),e[1]||(e[1]=l("span",{style:{"vertical-align":"middle"}},"CPU",-1))]),default:_(()=>[l("div",G,[l("table",I,[e[6]||(e[6]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"值")])])],-1)),l("tbody",null,[l("tr",null,[e[2]||(e[2]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"核心数")],-1)),l("td",S,[t(s).cpu?(i(),c("div",A,a(t(s).cpu.cpuNum),1)):d("",!0)])]),l("tr",null,[e[3]||(e[3]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"用户使用率")],-1)),l("td",D,[t(s).cpu?(i(),c("div",P,a(t(s).cpu.used)+"%",1)):d("",!0)])]),l("tr",null,[e[4]||(e[4]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统使用率")],-1)),l("td",$,[t(s).cpu?(i(),c("div",z,a(t(s).cpu.sys)+"%",1)):d("",!0)])]),l("tr",null,[e[5]||(e[5]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"当前空闲率")],-1)),l("td",E,[t(s).cpu?(i(),c("div",K,a(t(s).cpu.free)+"%",1)):d("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:12,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(g,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[7]||(e[7]=m()),e[8]||(e[8]=l("span",{style:{"vertical-align":"middle"}},"内存",-1))]),default:_(()=>[l("div",U,[l("table",q,[e[13]||(e[13]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"内存")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"JVM")])])],-1)),l("tbody",null,[l("tr",null,[e[9]||(e[9]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总内存")],-1)),l("td",H,[t(s).mem?(i(),c("div",O,a(t(s).mem.total)+"G",1)):d("",!0)]),l("td",Q,[t(s).jvm?(i(),c("div",R,a(t(s).jvm.total)+"M",1)):d("",!0)])]),l("tr",null,[e[10]||(e[10]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用内存")],-1)),l("td",W,[t(s).mem?(i(),c("div",X,a(t(s).mem.used)+"G",1)):d("",!0)]),l("td",Y,[t(s).jvm?(i(),c("div",Z,a(t(s).jvm.used)+"M",1)):d("",!0)])]),l("tr",null,[e[11]||(e[11]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"剩余内存")],-1)),l("td",F,[t(s).mem?(i(),c("div",ll,a(t(s).mem.free)+"G",1)):d("",!0)]),l("td",el,[t(s).jvm?(i(),c("div",sl,a(t(s).jvm.free)+"M",1)):d("",!0)])]),l("tr",null,[e[12]||(e[12]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"使用率")],-1)),l("td",tl,[t(s).mem?(i(),c("div",{key:0,class:b(["cell",{"text-danger":t(s).mem.usage>80}])},a(t(s).mem.usage)+"%",3)):d("",!0)]),l("td",al,[t(s).jvm?(i(),c("div",{key:0,class:b(["cell",{"text-danger":t(s).jvm.usage>80}])},a(t(s).jvm.usage)+"%",3)):d("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(k,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[14]||(e[14]=m()),e[15]||(e[15]=l("span",{style:{"vertical-align":"middle"}},"服务器信息",-1))]),default:_(()=>[l("div",cl,[l("table",il,[l("tbody",null,[l("tr",null,[e[16]||(e[16]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器名称")],-1)),l("td",dl,[t(s).sys?(i(),c("div",ol,a(t(s).sys.computerName),1)):d("",!0)]),e[17]||(e[17]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"操作系统")],-1)),l("td",_l,[t(s).sys?(i(),c("div",nl,a(t(s).sys.osName),1)):d("",!0)])]),l("tr",null,[e[18]||(e[18]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器IP")],-1)),l("td",rl,[t(s).sys?(i(),c("div",ul,a(t(s).sys.computerIp),1)):d("",!0)]),e[19]||(e[19]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统架构")],-1)),l("td",vl,[t(s).sys?(i(),c("div",ml,a(t(s).sys.osArch),1)):d("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(j,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[20]||(e[20]=m()),e[21]||(e[21]=l("span",{style:{"vertical-align":"middle"}},"Java虚拟机信息",-1))]),default:_(()=>[l("div",bl,[l("table",fl,[l("tbody",null,[l("tr",null,[e[22]||(e[22]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java名称")],-1)),l("td",pl,[t(s).jvm?(i(),c("div",hl,a(t(s).jvm.name),1)):d("",!0)]),e[23]||(e[23]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java版本")],-1)),l("td",yl,[t(s).jvm?(i(),c("div",gl,a(t(s).jvm.version),1)):d("",!0)])]),l("tr",null,[e[24]||(e[24]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"启动时间")],-1)),l("td",kl,[t(s).jvm?(i(),c("div",jl,a(t(s).jvm.startTime),1)):d("",!0)]),e[25]||(e[25]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行时长")],-1)),l("td",wl,[t(s).jvm?(i(),c("div",xl,a(t(s).jvm.runTime),1)):d("",!0)])]),l("tr",null,[e[26]||(e[26]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"安装路径")],-1)),l("td",Cl,[t(s).jvm?(i(),c("div",Nl,a(t(s).jvm.home),1)):d("",!0)])]),l("tr",null,[e[27]||(e[27]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"项目路径")],-1)),l("td",Ml,[t(s).sys?(i(),c("div",Tl,a(t(s).sys.userDir),1)):d("",!0)])]),l("tr",null,[e[28]||(e[28]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行参数")],-1)),l("td",Bl,[t(s).jvm?(i(),c("div",Jl,a(t(s).jvm.inputArgs),1)):d("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(w,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[29]||(e[29]=m()),e[30]||(e[30]=l("span",{style:{"vertical-align":"middle"}},"磁盘状态",-1))]),default:_(()=>[l("div",Vl,[l("table",Ll,[e[31]||(e[31]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符路径")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"文件系统")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符类型")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"可用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用百分比")])])],-1)),t(s).sysFiles?(i(),c("tbody",Gl,[(i(!0),c(B,null,J(t(s).sysFiles,(n,C)=>(i(),c("tr",{key:C},[l("td",Il,[l("div",Sl,a(n.dirName),1)]),l("td",Al,[l("div",Dl,a(n.sysTypeName),1)]),l("td",Pl,[l("div",$l,a(n.typeName),1)]),l("td",zl,[l("div",El,a(n.total),1)]),l("td",Kl,[l("div",Ul,a(n.free),1)]),l("td",ql,[l("div",Hl,a(n.used),1)]),l("td",Ol,[l("div",{class:b(["cell",{"text-danger":n.usage>80}])},a(n.usage)+"%",3)])]))),128))])):d("",!0)])])]),_:1})]),_:1})]),_:1})])}}};export{Wl as default};
