import{z as Qe,a as Ge,d as Je,r as s,A as We,I as Xe,e as r,F as fe,c as L,o as m,G as y,f as t,H as X,l as o,i as a,m as ce,J as Z,K as ee,j as _,C as S,n as i,h as B,t as ve,k as O,D as Y}from"./index-CdiCNU81.js";import{l as Ze,g as ye,e as el,f as ll,h as tl,i as ol,j as al,k as nl}from"./role-DqtqZXxX.js";import{t as ul,r as dl}from"./menu-BcsgTeEp.js";const rl={class:"app-container"},sl={class:"dialog-footer"},il={class:"dialog-footer"},pl=Qe({name:"Role"}),yl=Object.assign(pl,{setup(ml){const _e=Ge(),{proxy:v}=Je(),{sys_normal_disable:le}=v.useDict("sys_normal_disable"),te=s([]),g=s(!1),j=s(!0),T=s(!0),H=s([]),oe=s(!0),ae=s(!0),Q=s(0),U=s(""),N=s([]),I=s([]),$=s(!1),q=s(!1),A=s(!0),M=s(!1),E=s([]),w=s(!1),k=s(null),b=s(null),ge=s([{value:"1",label:"全部数据权限"},{value:"2",label:"自定数据权限"},{value:"3",label:"本部门数据权限"},{value:"4",label:"本部门及以下数据权限"},{value:"5",label:"仅本人数据权限"}]),ke=We({form:{},queryParams:{pageNum:1,pageSize:10,roleName:void 0,roleKey:void 0,status:void 0},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],roleKey:[{required:!0,message:"权限字符不能为空",trigger:"blur"}],roleSort:[{required:!0,message:"角色顺序不能为空",trigger:"blur"}]}}),{queryParams:f,form:u,rules:be}=Xe(ke);function V(){j.value=!0,Ze(v.addDateRange(f.value,N.value)).then(n=>{te.value=n.rows,Q.value=n.total,j.value=!1})}function P(){f.value.pageNum=1,V()}function Ve(){N.value=[],v.resetForm("queryRef"),P()}function ne(n){const e=n.roleId||H.value;v.$modal.confirm('是否确认删除角色编号为"'+e+'"的数据项?').then(function(){return el(e)}).then(()=>{V(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ce(){v.download("system/role/export",{...f.value},`role_${new Date().getTime()}.xlsx`)}function he(n){H.value=n.map(e=>e.roleId),oe.value=n.length!=1,ae.value=!n.length}function Se(n){let e=n.status==="0"?"启用":"停用";v.$modal.confirm('确认要"'+e+'""'+n.roleName+'"角色吗?').then(function(){return ll(n.roleId,n.status)}).then(()=>{v.$modal.msgSuccess(e+"成功")}).catch(function(){n.status=n.status==="0"?"1":"0"})}function we(n){_e.push("/system/role-auth/user/"+n.roleId)}function xe(){ul().then(n=>{I.value=n.data})}function Ke(){let n=b.value.getCheckedKeys(),e=b.value.getHalfCheckedKeys();return n.unshift.apply(n,e),n}function R(){k.value!=null&&k.value.setCheckedKeys([]),$.value=!1,q.value=!1,A.value=!0,M.value=!1,u.value={roleId:void 0,roleName:void 0,roleKey:void 0,roleSort:0,status:"0",menuIds:[],deptIds:[],menuCheckStrictly:!0,deptCheckStrictly:!0,remark:void 0},v.resetForm("roleRef")}function Ue(){R(),xe(),g.value=!0,U.value="添加角色"}function ue(n){R();const e=n.roleId||H.value,p=Ne(e);ye(e).then(d=>{u.value=d.data,u.value.roleSort=Number(u.value.roleSort),g.value=!0,Y(()=>{p.then(z=>{z.checkedKeys.forEach(J=>{Y(()=>{k.value.setChecked(J,!0,!1)})})})})}),U.value="修改角色"}function Ne(n){return dl(n).then(e=>(I.value=e.menus,e))}function Ie(n){return nl(n).then(e=>(E.value=e.depts,e))}function de(n,e){if(e=="menu"){let p=I.value;for(let d=0;d<p.length;d++)k.value.store.nodesMap[p[d].id].expanded=n}else if(e=="dept"){let p=E.value;for(let d=0;d<p.length;d++)b.value.store.nodesMap[p[d].id].expanded=n}}function re(n,e){e=="menu"?k.value.setCheckedNodes(n?I.value:[]):e=="dept"&&b.value.setCheckedNodes(n?E.value:[])}function se(n,e){e=="menu"?u.value.menuCheckStrictly=!!n:e=="dept"&&(u.value.deptCheckStrictly=!!n)}function ie(){let n=k.value.getCheckedKeys(),e=k.value.getHalfCheckedKeys();return n.unshift.apply(n,e),n}function Re(){v.$refs.roleRef.validate(n=>{n&&(u.value.roleId!=null?(u.value.menuIds=ie(),tl(u.value).then(e=>{v.$modal.msgSuccess("修改成功"),g.value=!1,V()})):(u.value.menuIds=ie(),ol(u.value).then(e=>{v.$modal.msgSuccess("新增成功"),g.value=!1,V()})))})}function De(){g.value=!1,R()}function Te(n){n!=="2"&&b.value.setCheckedKeys([])}function $e(n){R();const e=Ie(n.roleId);ye(n.roleId).then(p=>{u.value=p.data,w.value=!0,Y(()=>{e.then(d=>{Y(()=>{b.value&&b.value.setCheckedKeys(d.checkedKeys)})})})}),U.value="分配数据权限"}function qe(){u.value.roleId!=null&&(u.value.deptIds=Ke(),al(u.value).then(n=>{v.$modal.msgSuccess("修改成功"),w.value=!1,V()}))}function Ae(){w.value=!1,R()}return V(),(n,e)=>{const p=r("el-input"),d=r("el-form-item"),z=r("el-option"),G=r("el-select"),J=r("el-date-picker"),c=r("el-button"),W=r("el-form"),F=r("el-col"),Me=r("right-toolbar"),Ee=r("el-row"),C=r("el-table-column"),Pe=r("el-switch"),D=r("el-tooltip"),ze=r("el-table"),Fe=r("pagination"),Le=r("question-filled"),Be=r("el-icon"),Oe=r("el-input-number"),Ye=r("el-radio"),je=r("el-radio-group"),x=r("el-checkbox"),pe=r("el-tree"),me=r("el-dialog"),h=fe("hasPermi"),He=fe("loading");return m(),L("div",rl,[y(t(W,{model:o(f),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[t(d,{label:"角色名称",prop:"roleName"},{default:a(()=>[t(p,{modelValue:o(f).roleName,"onUpdate:modelValue":e[0]||(e[0]=l=>o(f).roleName=l),placeholder:"请输入角色名称",clearable:"",style:{width:"240px"},onKeyup:ce(P,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"权限字符",prop:"roleKey"},{default:a(()=>[t(p,{modelValue:o(f).roleKey,"onUpdate:modelValue":e[1]||(e[1]=l=>o(f).roleKey=l),placeholder:"请输入权限字符",clearable:"",style:{width:"240px"},onKeyup:ce(P,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"状态",prop:"status"},{default:a(()=>[t(G,{modelValue:o(f).status,"onUpdate:modelValue":e[2]||(e[2]=l=>o(f).status=l),placeholder:"角色状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(m(!0),L(Z,null,ee(o(le),l=>(m(),_(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"创建时间",style:{width:"308px"}},{default:a(()=>[t(J,{modelValue:o(N),"onUpdate:modelValue":e[3]||(e[3]=l=>S(N)?N.value=l:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(d,null,{default:a(()=>[t(c,{type:"primary",icon:"Search",onClick:P},{default:a(()=>e[29]||(e[29]=[i("搜索")])),_:1,__:[29]}),t(c,{icon:"Refresh",onClick:Ve},{default:a(()=>e[30]||(e[30]=[i("重置")])),_:1,__:[30]})]),_:1})]),_:1},8,["model"]),[[X,o(T)]]),t(Ee,{gutter:10,class:"mb8"},{default:a(()=>[t(F,{span:1.5},{default:a(()=>[y((m(),_(c,{type:"primary",plain:"",icon:"Plus",onClick:Ue},{default:a(()=>e[31]||(e[31]=[i("新增")])),_:1,__:[31]})),[[h,["system:role:add"]]])]),_:1}),t(F,{span:1.5},{default:a(()=>[y((m(),_(c,{type:"success",plain:"",icon:"Edit",disabled:o(oe),onClick:ue},{default:a(()=>e[32]||(e[32]=[i("修改")])),_:1,__:[32]},8,["disabled"])),[[h,["system:role:edit"]]])]),_:1}),t(F,{span:1.5},{default:a(()=>[y((m(),_(c,{type:"danger",plain:"",icon:"Delete",disabled:o(ae),onClick:ne},{default:a(()=>e[33]||(e[33]=[i("删除")])),_:1,__:[33]},8,["disabled"])),[[h,["system:role:remove"]]])]),_:1}),t(F,{span:1.5},{default:a(()=>[y((m(),_(c,{type:"warning",plain:"",icon:"Download",onClick:Ce},{default:a(()=>e[34]||(e[34]=[i("导出")])),_:1,__:[34]})),[[h,["system:role:export"]]])]),_:1}),t(Me,{showSearch:o(T),"onUpdate:showSearch":e[4]||(e[4]=l=>S(T)?T.value=l:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),y((m(),_(ze,{data:o(te),onSelectionChange:he},{default:a(()=>[t(C,{type:"selection",width:"55",align:"center"}),t(C,{label:"角色编号",prop:"roleId",width:"120"}),t(C,{label:"角色名称",prop:"roleName","show-overflow-tooltip":!0,width:"150"}),t(C,{label:"权限字符",prop:"roleKey","show-overflow-tooltip":!0,width:"150"}),t(C,{label:"显示顺序",prop:"roleSort",width:"100"}),t(C,{label:"状态",align:"center",width:"100"},{default:a(l=>[t(Pe,{modelValue:l.row.status,"onUpdate:modelValue":K=>l.row.status=K,"active-value":"0","inactive-value":"1",onChange:K=>Se(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(C,{label:"创建时间",align:"center",prop:"createTime"},{default:a(l=>[B("span",null,ve(n.parseTime(l.row.createTime)),1)]),_:1}),t(C,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(l=>[l.row.roleId!==1?(m(),_(D,{key:0,content:"修改",placement:"top"},{default:a(()=>[y(t(c,{link:"",type:"primary",icon:"Edit",onClick:K=>ue(l.row)},null,8,["onClick"]),[[h,["system:role:edit"]]])]),_:2},1024)):O("",!0),l.row.roleId!==1?(m(),_(D,{key:1,content:"删除",placement:"top"},{default:a(()=>[y(t(c,{link:"",type:"primary",icon:"Delete",onClick:K=>ne(l.row)},null,8,["onClick"]),[[h,["system:role:remove"]]])]),_:2},1024)):O("",!0),l.row.roleId!==1?(m(),_(D,{key:2,content:"数据权限",placement:"top"},{default:a(()=>[y(t(c,{link:"",type:"primary",icon:"CircleCheck",onClick:K=>$e(l.row)},null,8,["onClick"]),[[h,["system:role:edit"]]])]),_:2},1024)):O("",!0),l.row.roleId!==1?(m(),_(D,{key:3,content:"分配用户",placement:"top"},{default:a(()=>[y(t(c,{link:"",type:"primary",icon:"User",onClick:K=>we(l.row)},null,8,["onClick"]),[[h,["system:role:edit"]]])]),_:2},1024)):O("",!0)]),_:1})]),_:1},8,["data"])),[[He,o(j)]]),y(t(Fe,{total:o(Q),page:o(f).pageNum,"onUpdate:page":e[5]||(e[5]=l=>o(f).pageNum=l),limit:o(f).pageSize,"onUpdate:limit":e[6]||(e[6]=l=>o(f).pageSize=l),onPagination:V},null,8,["total","page","limit"]),[[X,o(Q)>0]]),t(me,{title:o(U),modelValue:o(g),"onUpdate:modelValue":e[18]||(e[18]=l=>S(g)?g.value=l:null),width:"500px","append-to-body":""},{footer:a(()=>[B("div",sl,[t(c,{type:"primary",onClick:Re},{default:a(()=>e[39]||(e[39]=[i("确 定")])),_:1,__:[39]}),t(c,{onClick:De},{default:a(()=>e[40]||(e[40]=[i("取 消")])),_:1,__:[40]})])]),default:a(()=>[t(W,{ref:"roleRef",model:o(u),rules:o(be),"label-width":"100px"},{default:a(()=>[t(d,{label:"角色名称",prop:"roleName"},{default:a(()=>[t(p,{modelValue:o(u).roleName,"onUpdate:modelValue":e[7]||(e[7]=l=>o(u).roleName=l),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),t(d,{prop:"roleKey"},{label:a(()=>[B("span",null,[t(D,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)",placement:"top"},{default:a(()=>[t(Be,null,{default:a(()=>[t(Le)]),_:1})]),_:1}),e[35]||(e[35]=i(" 权限字符 "))])]),default:a(()=>[t(p,{modelValue:o(u).roleKey,"onUpdate:modelValue":e[8]||(e[8]=l=>o(u).roleKey=l),placeholder:"请输入权限字符"},null,8,["modelValue"])]),_:1}),t(d,{label:"角色顺序",prop:"roleSort"},{default:a(()=>[t(Oe,{modelValue:o(u).roleSort,"onUpdate:modelValue":e[9]||(e[9]=l=>o(u).roleSort=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),t(d,{label:"状态"},{default:a(()=>[t(je,{modelValue:o(u).status,"onUpdate:modelValue":e[10]||(e[10]=l=>o(u).status=l)},{default:a(()=>[(m(!0),L(Z,null,ee(o(le),l=>(m(),_(Ye,{key:l.value,value:l.value},{default:a(()=>[i(ve(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"菜单权限"},{default:a(()=>[t(x,{modelValue:o($),"onUpdate:modelValue":e[11]||(e[11]=l=>S($)?$.value=l:null),onChange:e[12]||(e[12]=l=>de(l,"menu"))},{default:a(()=>e[36]||(e[36]=[i("展开/折叠")])),_:1,__:[36]},8,["modelValue"]),t(x,{modelValue:o(q),"onUpdate:modelValue":e[13]||(e[13]=l=>S(q)?q.value=l:null),onChange:e[14]||(e[14]=l=>re(l,"menu"))},{default:a(()=>e[37]||(e[37]=[i("全选/全不选")])),_:1,__:[37]},8,["modelValue"]),t(x,{modelValue:o(u).menuCheckStrictly,"onUpdate:modelValue":e[15]||(e[15]=l=>o(u).menuCheckStrictly=l),onChange:e[16]||(e[16]=l=>se(l,"menu"))},{default:a(()=>e[38]||(e[38]=[i("父子联动")])),_:1,__:[38]},8,["modelValue"]),t(pe,{class:"tree-border",data:o(I),"show-checkbox":"",ref_key:"menuRef",ref:k,"node-key":"id","check-strictly":!o(u).menuCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1}),t(d,{label:"备注"},{default:a(()=>[t(p,{modelValue:o(u).remark,"onUpdate:modelValue":e[17]||(e[17]=l=>o(u).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(me,{title:o(U),modelValue:o(w),"onUpdate:modelValue":e[28]||(e[28]=l=>S(w)?w.value=l:null),width:"500px","append-to-body":""},{footer:a(()=>[B("div",il,[t(c,{type:"primary",onClick:qe},{default:a(()=>e[44]||(e[44]=[i("确 定")])),_:1,__:[44]}),t(c,{onClick:Ae},{default:a(()=>e[45]||(e[45]=[i("取 消")])),_:1,__:[45]})])]),default:a(()=>[t(W,{model:o(u),"label-width":"80px"},{default:a(()=>[t(d,{label:"角色名称"},{default:a(()=>[t(p,{modelValue:o(u).roleName,"onUpdate:modelValue":e[19]||(e[19]=l=>o(u).roleName=l),disabled:!0},null,8,["modelValue"])]),_:1}),t(d,{label:"权限字符"},{default:a(()=>[t(p,{modelValue:o(u).roleKey,"onUpdate:modelValue":e[20]||(e[20]=l=>o(u).roleKey=l),disabled:!0},null,8,["modelValue"])]),_:1}),t(d,{label:"权限范围"},{default:a(()=>[t(G,{modelValue:o(u).dataScope,"onUpdate:modelValue":e[21]||(e[21]=l=>o(u).dataScope=l),onChange:Te},{default:a(()=>[(m(!0),L(Z,null,ee(o(ge),l=>(m(),_(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),y(t(d,{label:"数据权限"},{default:a(()=>[t(x,{modelValue:o(A),"onUpdate:modelValue":e[22]||(e[22]=l=>S(A)?A.value=l:null),onChange:e[23]||(e[23]=l=>de(l,"dept"))},{default:a(()=>e[41]||(e[41]=[i("展开/折叠")])),_:1,__:[41]},8,["modelValue"]),t(x,{modelValue:o(M),"onUpdate:modelValue":e[24]||(e[24]=l=>S(M)?M.value=l:null),onChange:e[25]||(e[25]=l=>re(l,"dept"))},{default:a(()=>e[42]||(e[42]=[i("全选/全不选")])),_:1,__:[42]},8,["modelValue"]),t(x,{modelValue:o(u).deptCheckStrictly,"onUpdate:modelValue":e[26]||(e[26]=l=>o(u).deptCheckStrictly=l),onChange:e[27]||(e[27]=l=>se(l,"dept"))},{default:a(()=>e[43]||(e[43]=[i("父子联动")])),_:1,__:[43]},8,["modelValue"]),t(pe,{class:"tree-border",data:o(E),"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:b,"node-key":"id","check-strictly":!o(u).deptCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1},512),[[X,o(u).dataScope==2]])]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});export{yl as default};
