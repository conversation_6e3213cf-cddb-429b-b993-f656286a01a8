import{_ as Oe,d as qe,A as Be,I as Se,x as Z,r as Pe,e as c,c as D,o as n,f as t,h as x,i as u,l as g,C as P,G as F,j as i,k as o,J as w,K as I,t as K,n as s,H as j}from"./index-CdiCNU81.js";import{d as Fe,i as W}from"./index-DL_ceqJA.js";import Ke from"./IconsDialog-BIt7Xo0K.js";import je from"./TreeNodeDialog-i_qbC-L2.js";const tl={formRef:"formRef",formModel:"formData",size:"default",labelPosition:"right",labelWidth:100,formRules:"rules",gutter:15,disabled:!1,span:24,formBtns:!0},_=[{label:"单行文本",tag:"el-input",tagIcon:"input",type:"text",placeholder:"请输入",defaultValue:void 0,span:24,labelWidth:null,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/input"},{label:"多行文本",tag:"el-input",tagIcon:"textarea",type:"textarea",placeholder:"请输入",defaultValue:void 0,span:24,labelWidth:null,autosize:{minRows:4,maxRows:4},style:{width:"100%"},maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/input"},{label:"密码",tag:"el-input",tagIcon:"password",type:"password",placeholder:"请输入",defaultValue:void 0,span:24,"show-password":!0,labelWidth:null,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/input"},{label:"计数器",tag:"el-input-number",tagIcon:"number",placeholder:"",defaultValue:void 0,span:24,labelWidth:null,min:void 0,max:void 0,step:void 0,"step-strictly":!1,precision:void 0,"controls-position":"",disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/input-number"}],ee=[{label:"下拉选择",tag:"el-select",tagIcon:"select",placeholder:"请选择",defaultValue:void 0,span:24,labelWidth:null,style:{width:"100%"},clearable:!0,disabled:!1,required:!0,filterable:!1,multiple:!1,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/select"},{label:"级联选择",tag:"el-cascader",tagIcon:"cascader",placeholder:"请选择",defaultValue:[],span:24,labelWidth:null,style:{width:"100%"},props:{props:{multiple:!1}},"show-all-levels":!0,disabled:!1,clearable:!0,filterable:!1,required:!0,options:[{id:1,value:1,label:"选项1",children:[{id:2,value:2,label:"选项1-1"}]}],dataType:"dynamic",labelKey:"label",valueKey:"value",childrenKey:"children",separator:"/",regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/cascader"},{label:"单选框组",tag:"el-radio-group",tagIcon:"radio",defaultValue:0,span:24,labelWidth:null,style:{},optionType:"default",border:!1,size:"default",disabled:!1,required:!0,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/radio"},{label:"多选框组",tag:"el-checkbox-group",tagIcon:"checkbox",defaultValue:[],span:24,labelWidth:null,style:{},optionType:"default",border:!1,size:"default",disabled:!1,required:!0,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/checkbox"},{label:"开关",tag:"el-switch",tagIcon:"switch",defaultValue:!1,span:24,labelWidth:null,style:{},disabled:!1,required:!0,"active-text":"","inactive-text":"","active-color":null,"inactive-color":null,"active-value":!0,"inactive-value":!1,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/switch"},{label:"滑块",tag:"el-slider",tagIcon:"slider",defaultValue:null,span:24,labelWidth:null,disabled:!1,required:!0,min:0,max:100,step:1,"show-stops":!1,range:!1,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/slider"},{label:"时间选择",tag:"el-time-picker",tagIcon:"time",placeholder:"请选择",defaultValue:"",span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,format:"HH:mm:ss","value-format":"HH:mm:ss",regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/time-picker"},{label:"时间范围",tag:"el-time-picker",tagIcon:"time-range",defaultValue:null,span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,"is-range":!0,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm:ss","value-format":"HH:mm:ss",regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/time-picker"},{label:"日期选择",tag:"el-date-picker",tagIcon:"date",placeholder:"请选择",defaultValue:null,type:"date",span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",readonly:!1,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/date-picker"},{label:"日期范围",tag:"el-date-picker",tagIcon:"date-range",defaultValue:null,span:24,labelWidth:null,style:{width:"100%"},type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:!1,clearable:!0,required:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",readonly:!1,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/date-picker"},{label:"评分",tag:"el-rate",tagIcon:"rate",defaultValue:0,span:24,labelWidth:null,style:{},max:5,"allow-half":!1,"show-text":!1,"show-score":!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/rate"},{label:"颜色选择",tag:"el-color-picker",tagIcon:"color",defaultValue:null,labelWidth:null,"show-alpha":!1,"color-format":"",disabled:!1,required:!0,size:"default",regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/color-picker"},{label:"上传",tag:"el-upload",tagIcon:"upload",action:"https://jsonplaceholder.typicode.com/posts/",defaultValue:null,labelWidth:null,disabled:!1,required:!0,accept:"",name:"file","auto-upload":!0,showTip:!1,buttonText:"点击上传",fileSize:2,sizeUnit:"MB","list-type":"text",multiple:!1,regList:[],changeTag:!0,document:"https://element-plus.org/zh-CN/component/upload",tip:"只能上传不超过 2MB 的文件",style:{width:"100%"}}],ul=[{layout:"rowFormItem",tagIcon:"row",type:"default",justify:"start",align:"top",label:"行容器",layoutTree:!0,children:[],document:"https://element-plus.org/zh-CN/component/layout"},{layout:"colFormItem",label:"按钮",changeTag:!0,labelWidth:null,tag:"el-button",tagIcon:"button",span:24,default:"主要按钮",type:"primary",icon:"Search",size:"default",disabled:!1,document:"https://element-plus.org/zh-CN/component/button"}],nl={"el-input":"blur","el-input-number":"blur","el-select":"change","el-radio-group":"change","el-checkbox-group":"change","el-cascader":"change","el-time-picker":"change","el-date-picker":"change","el-rate":"change"},He={class:"right-board"},$e={class:"field-box"},Ae=["href"],Ge=["onClick"],Je={key:2},Ee={class:"node-label"},Qe=["onClick"],Xe={__name:"RightPanel",props:{showField:Boolean,activeData:Object,formConf:Object},emits:["tag-change"],setup(e,{emit:le}){const{proxy:ae}=qe(),H={date:"YYYY-MM-DD",week:"YYYY 第 ww 周",month:"YYYY-MM",year:"YYYY",datetime:"YYYY-MM-DD HH:mm:ss",daterange:"YYYY-MM-DD",monthrange:"YYYY-MM",datetimerange:"YYYY-MM-DD HH:mm:ss"},f=e,te=Be({currentTab:"field",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:"日(date)",value:"date"},{label:"周(week)",value:"week"},{label:"月(month)",value:"month"},{label:"年(year)",value:"year"},{label:"日期时间(datetime)",value:"datetime"}],dateRangeTypeOptions:[{label:"日期范围(daterange)",value:"daterange"},{label:"月范围(monthrange)",value:"monthrange"},{label:"日期时间范围(datetimerange)",value:"datetimerange"}],colorFormatOptions:[{label:"hex",value:"hex"},{label:"rgb",value:"rgb"},{label:"rgba",value:"rgba"},{label:"hsv",value:"hsv"},{label:"hsl",value:"hsl"}],justifyOptions:[{label:"start",value:"start"},{label:"end",value:"end"},{label:"center",value:"center"},{label:"space-around",value:"space-around"},{label:"space-between",value:"space-between"}],layoutTreeProps:{label(m,l){return m.componentName||`${m.label}: ${m.vModel}`}}}),{currentTab:z,currentNode:O,dialogVisible:Y,iconsVisible:L,currentIconModel:q,dateTypeOptions:ue,dateRangeTypeOptions:ne,colorFormatOptions:de,justifyOptions:oe,layoutTreeProps:ie}=Se(te),me=Z(()=>f.activeData.document||"https://element-plus.org/zh-CN/guide/installation"),re=Z(()=>f.activeData.type!==void 0&&f.activeData.tag==="el-date-picker"?f.activeData["start-placeholder"]===void 0?ue.value:ne.value:[]),fe=Pe([{label:"输入型组件",options:_},{label:"选择型组件",options:ee}]),ce=le;function se(){f.activeData.regList.push({pattern:"",message:""})}function ve(){f.activeData.options.push({label:"",value:""})}function be(){++ae.idGlobal,Y.value=!0,O.value=f.activeData.options}function Ve(m,{node:l,data:y,store:M}){return m("div",{class:"custom-tree-node"},[m("span",l.label),m("span",{class:"node-operation"},[m(c("el-link"),{type:"primary",icon:"Plus",underline:!1,onClick:()=>{De(y)}}),m(c("el-link"),{type:"danger",icon:"Delete",underline:!1,style:"margin-left: 5px;",onClick:()=>{ge(l,y)}})])])}function De(m){m.children||(m.children=[]),Y.value=!0,O.value=m.children}function ge(m,l){const{parent:y}=m,M=y.data.children||y.data,S=M.findIndex(N=>N.id===l.id);M.splice(S,1)}function ye(m){O.value.push(m)}function ke(m,l){m.value=W(l)?+l:l}function B(m){return Array.isArray(m)?m.join(","):["string","number"].indexOf(m)>-1?m:typeof m=="boolean"?`${m}`:m}function pe(m){Array.isArray(f.activeData.defaultValue)?f.activeData.defaultValue=m.split(",").map(l=>W(l)?+l:l):["true","false"].indexOf(m)>-1?f.activeData.defaultValue=JSON.parse(m):f.activeData.defaultValue=W(m)?+m:m}function $(m,l){["true","false"].indexOf(m)>-1?f.activeData[l]=JSON.parse(m):f.activeData[l]=W(m)?+m:m}function A(m,l){const y=l==="week"?H.date:m;f.activeData.defaultValue=null,f.activeData["value-format"]=y,f.activeData.format=m}function xe(m){f.formConf.span=m}function we(m){f.activeData.defaultValue=m?[]:""}function he(m){A(H[m],m)}function Ue(m){f.activeData.defaultValue=m?[f.activeData.min,f.activeData.max]:f.activeData.min}function Ce(m){m&&(f.activeData["show-score"]=!1)}function Te(m){m&&(f.activeData["show-text"]=!1)}function Ie(m){f.activeData.defaultValue=null,f.activeData["show-alpha"]=m.indexOf("a")>-1,f.activeData.renderKey=+new Date}function G(m){L.value=!0,q.value=m}function ze(m){f.activeData[q.value]=m}function Ye(m){let l=_.find(y=>y.tagIcon===m);l||(l=ee.find(y=>y.tagIcon===m)),ce("tag-change",l)}return(m,l)=>{const y=c("el-tab-pane"),M=c("el-tabs"),S=c("Link"),N=c("el-icon"),J=c("svg-icon"),k=c("el-option"),Me=c("el-option-group"),C=c("el-select"),d=c("el-form-item"),r=c("el-input"),Ne=c("el-slider"),p=c("el-input-number"),b=c("el-radio-button"),U=c("el-radio-group"),R=c("el-button"),T=c("el-divider"),Re=c("Remove"),E=c("el-tree"),Q=c("el-color-picker"),v=c("el-switch"),Le=c("Close"),X=c("el-form"),We=c("el-scrollbar");return n(),D("div",He,[t(M,{modelValue:g(z),"onUpdate:modelValue":l[0]||(l[0]=a=>P(z)?z.value=a:null),stretch:"",class:"center-tabs"},{default:u(()=>[t(y,{label:"组件属性",name:"field"}),t(y,{label:"表单属性",name:"form"})]),_:1},8,["modelValue"]),x("div",$e,[x("a",{class:"document-link",target:"_blank",href:g(me),title:"查看组件文档"},[t(N,null,{default:u(()=>[t(S)]),_:1})],8,Ae),t(We,{class:"right-scrollbar"},{default:u(()=>[F(t(X,{size:"default","label-width":"90px","label-position":"top",style:{}},{default:u(()=>[e.activeData.changeTag?(n(),i(d,{key:0,label:"组件类型"},{default:u(()=>[t(C,{modelValue:e.activeData.tagIcon,"onUpdate:modelValue":l[1]||(l[1]=a=>e.activeData.tagIcon=a),placeholder:"请选择组件类型",style:{width:"100%"},onChange:Ye},{default:u(()=>[(n(!0),D(w,null,I(g(fe),a=>(n(),i(Me,{key:a.label,label:a.label},{default:u(()=>[(n(!0),D(w,null,I(a.options,V=>(n(),i(k,{key:V.label,label:V.label,value:V.tagIcon},{default:u(()=>[t(J,{class:"node-icon","icon-class":V.tagIcon,style:{"margin-right":"10px"}},null,8,["icon-class"]),x("span",null,K(V.label),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.vModel!==void 0?(n(),i(d,{key:1,label:"字段名"},{default:u(()=>[t(r,{modelValue:e.activeData.vModel,"onUpdate:modelValue":l[2]||(l[2]=a=>e.activeData.vModel=a),placeholder:"请输入字段名（v-model）"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.componentName!==void 0?(n(),i(d,{key:2,label:"组件名"},{default:u(()=>[s(K(e.activeData.componentName),1)]),_:1})):o("",!0),e.activeData.label!==void 0?(n(),i(d,{key:3,label:"标题"},{default:u(()=>[t(r,{modelValue:e.activeData.label,"onUpdate:modelValue":l[3]||(l[3]=a=>e.activeData.label=a),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.placeholder!==void 0?(n(),i(d,{key:4,label:"占位提示"},{default:u(()=>[t(r,{modelValue:e.activeData.placeholder,"onUpdate:modelValue":l[4]||(l[4]=a=>e.activeData.placeholder=a),placeholder:"请输入占位提示"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["start-placeholder"]!==void 0?(n(),i(d,{key:5,label:"开始占位"},{default:u(()=>[t(r,{modelValue:e.activeData["start-placeholder"],"onUpdate:modelValue":l[5]||(l[5]=a=>e.activeData["start-placeholder"]=a),placeholder:"请输入占位提示"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["end-placeholder"]!==void 0?(n(),i(d,{key:6,label:"结束占位"},{default:u(()=>[t(r,{modelValue:e.activeData["end-placeholder"],"onUpdate:modelValue":l[6]||(l[6]=a=>e.activeData["end-placeholder"]=a),placeholder:"请输入占位提示"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.span!==void 0?(n(),i(d,{key:7,label:"表单栅格"},{default:u(()=>[t(Ne,{modelValue:e.activeData.span,"onUpdate:modelValue":l[7]||(l[7]=a=>e.activeData.span=a),max:24,min:1,marks:{12:""},onChange:xe},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.layout==="rowFormItem"?(n(),i(d,{key:8,label:"栅格间隔"},{default:u(()=>[t(p,{modelValue:e.activeData.gutter,"onUpdate:modelValue":l[8]||(l[8]=a=>e.activeData.gutter=a),min:0,placeholder:"栅格间隔"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.justify!==void 0?(n(),i(d,{key:9,label:"水平排列"},{default:u(()=>[t(C,{modelValue:e.activeData.justify,"onUpdate:modelValue":l[9]||(l[9]=a=>e.activeData.justify=a),placeholder:"请选择水平排列",style:{width:"100%"}},{default:u(()=>[(n(!0),D(w,null,I(g(oe),(a,V)=>(n(),i(k,{key:V,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.align!==void 0?(n(),i(d,{key:10,label:"垂直排列"},{default:u(()=>[t(U,{modelValue:e.activeData.align,"onUpdate:modelValue":l[10]||(l[10]=a=>e.activeData.align=a)},{default:u(()=>[t(b,{label:"top"}),t(b,{label:"middle"}),t(b,{label:"bottom"})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.labelWidth!==void 0?(n(),i(d,{key:11,label:"标签宽度"},{default:u(()=>[t(r,{modelValue:e.activeData.labelWidth,"onUpdate:modelValue":l[11]||(l[11]=a=>e.activeData.labelWidth=a),modelModifiers:{number:!0},type:"number",placeholder:"请输入标签宽度"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.style&&e.activeData.style.width!==void 0?(n(),i(d,{key:12,label:"组件宽度"},{default:u(()=>[t(r,{modelValue:e.activeData.style.width,"onUpdate:modelValue":l[12]||(l[12]=a=>e.activeData.style.width=a),placeholder:"请输入组件宽度",clearable:""},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.vModel!==void 0?(n(),i(d,{key:13,label:"默认值"},{default:u(()=>[t(r,{value:B(e.activeData.defaultValue),placeholder:"请输入默认值",onInput:pe},null,8,["value"])]),_:1})):o("",!0),e.activeData.tag==="el-checkbox-group"?(n(),i(d,{key:14,label:"至少应选"},{default:u(()=>[t(p,{value:e.activeData.min,min:0,placeholder:"至少应选",onInput:l[13]||(l[13]=a=>m.$set(e.activeData,"min",a||void 0))},null,8,["value"])]),_:1})):o("",!0),e.activeData.tag==="el-checkbox-group"?(n(),i(d,{key:15,label:"最多可选"},{default:u(()=>[t(p,{value:e.activeData.max,min:0,placeholder:"最多可选",onInput:l[14]||(l[14]=a=>m.$set(e.activeData,"max",a||void 0))},null,8,["value"])]),_:1})):o("",!0),e.activeData.prepend!==void 0?(n(),i(d,{key:16,label:"前缀"},{default:u(()=>[t(r,{modelValue:e.activeData.prepend,"onUpdate:modelValue":l[15]||(l[15]=a=>e.activeData.prepend=a),placeholder:"请输入前缀"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.append!==void 0?(n(),i(d,{key:17,label:"后缀"},{default:u(()=>[t(r,{modelValue:e.activeData.append,"onUpdate:modelValue":l[16]||(l[16]=a=>e.activeData.append=a),placeholder:"请输入后缀"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["prefix-icon"]!==void 0?(n(),i(d,{key:18,label:"前图标"},{default:u(()=>[t(r,{modelValue:e.activeData["prefix-icon"],"onUpdate:modelValue":l[18]||(l[18]=a=>e.activeData["prefix-icon"]=a),placeholder:"请输入前图标名称"},{append:u(()=>[t(R,{icon:"Pointer",onClick:l[17]||(l[17]=a=>G("prefix-icon"))},{default:u(()=>l[86]||(l[86]=[s(" 选择 ")])),_:1,__:[86]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData["suffix-icon"]!==void 0?(n(),i(d,{key:19,label:"后图标"},{default:u(()=>[t(r,{modelValue:e.activeData["suffix-icon"],"onUpdate:modelValue":l[20]||(l[20]=a=>e.activeData["suffix-icon"]=a),placeholder:"请输入后图标名称"},{append:u(()=>[t(R,{icon:"Pointer",onClick:l[19]||(l[19]=a=>G("suffix-icon"))},{default:u(()=>l[87]||(l[87]=[s(" 选择 ")])),_:1,__:[87]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-cascader"?(n(),i(d,{key:20,label:"选项分隔符"},{default:u(()=>[t(r,{modelValue:e.activeData.separator,"onUpdate:modelValue":l[21]||(l[21]=a=>e.activeData.separator=a),placeholder:"请输入选项分隔符"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.autosize!==void 0?(n(),i(d,{key:21,label:"最小行数"},{default:u(()=>[t(p,{modelValue:e.activeData.autosize.minRows,"onUpdate:modelValue":l[22]||(l[22]=a=>e.activeData.autosize.minRows=a),min:1,placeholder:"最小行数"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.autosize!==void 0?(n(),i(d,{key:22,label:"最大行数"},{default:u(()=>[t(p,{modelValue:e.activeData.autosize.maxRows,"onUpdate:modelValue":l[23]||(l[23]=a=>e.activeData.autosize.maxRows=a),min:1,placeholder:"最大行数"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.min!==void 0?(n(),i(d,{key:23,label:"最小值"},{default:u(()=>[t(p,{modelValue:e.activeData.min,"onUpdate:modelValue":l[24]||(l[24]=a=>e.activeData.min=a),placeholder:"最小值"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.max!==void 0?(n(),i(d,{key:24,label:"最大值"},{default:u(()=>[t(p,{modelValue:e.activeData.max,"onUpdate:modelValue":l[25]||(l[25]=a=>e.activeData.max=a),placeholder:"最大值"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.step!==void 0?(n(),i(d,{key:25,label:"步长"},{default:u(()=>[t(p,{modelValue:e.activeData.step,"onUpdate:modelValue":l[26]||(l[26]=a=>e.activeData.step=a),placeholder:"步数"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-input-number"?(n(),i(d,{key:26,label:"精度"},{default:u(()=>[t(p,{modelValue:e.activeData.precision,"onUpdate:modelValue":l[27]||(l[27]=a=>e.activeData.precision=a),min:0,placeholder:"精度"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-input-number"?(n(),i(d,{key:27,label:"按钮位置"},{default:u(()=>[t(U,{modelValue:e.activeData["controls-position"],"onUpdate:modelValue":l[28]||(l[28]=a=>e.activeData["controls-position"]=a)},{default:u(()=>[t(b,{label:""},{default:u(()=>l[88]||(l[88]=[s(" 默认 ")])),_:1,__:[88]}),t(b,{label:"right"},{default:u(()=>l[89]||(l[89]=[s(" 右侧 ")])),_:1,__:[89]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.maxlength!==void 0?(n(),i(d,{key:28,label:"最多输入"},{default:u(()=>[t(r,{modelValue:e.activeData.maxlength,"onUpdate:modelValue":l[29]||(l[29]=a=>e.activeData.maxlength=a),placeholder:"请输入字符长度"},{default:u(()=>l[90]||(l[90]=[x("template",{slot:"append"},[s(" 个字符 ")],-1)])),_:1,__:[90]},8,["modelValue"])]),_:1})):o("",!0),e.activeData["active-text"]!==void 0?(n(),i(d,{key:29,label:"开启提示"},{default:u(()=>[t(r,{modelValue:e.activeData["active-text"],"onUpdate:modelValue":l[30]||(l[30]=a=>e.activeData["active-text"]=a),placeholder:"请输入开启提示"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["inactive-text"]!==void 0?(n(),i(d,{key:30,label:"关闭提示"},{default:u(()=>[t(r,{modelValue:e.activeData["inactive-text"],"onUpdate:modelValue":l[31]||(l[31]=a=>e.activeData["inactive-text"]=a),placeholder:"请输入关闭提示"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["active-value"]!==void 0?(n(),i(d,{key:31,label:"开启值"},{default:u(()=>[t(r,{value:B(e.activeData["active-value"]),placeholder:"请输入开启值",onInput:l[32]||(l[32]=a=>$(a,"active-value"))},null,8,["value"])]),_:1})):o("",!0),e.activeData["inactive-value"]!==void 0?(n(),i(d,{key:32,label:"关闭值"},{default:u(()=>[t(r,{value:B(e.activeData["inactive-value"]),placeholder:"请输入关闭值",onInput:l[33]||(l[33]=a=>$(a,"inactive-value"))},null,8,["value"])]),_:1})):o("",!0),e.activeData.type!==void 0&&e.activeData.tag==="el-date-picker"?(n(),i(d,{key:33,label:"时间类型"},{default:u(()=>[t(C,{modelValue:e.activeData.type,"onUpdate:modelValue":l[34]||(l[34]=a=>e.activeData.type=a),placeholder:"请选择时间类型",style:{width:"100%"},onChange:he},{default:u(()=>[(n(!0),D(w,null,I(g(re),(a,V)=>(n(),i(k,{key:V,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.name!==void 0?(n(),i(d,{key:34,label:"文件字段名"},{default:u(()=>[t(r,{modelValue:e.activeData.name,"onUpdate:modelValue":l[35]||(l[35]=a=>e.activeData.name=a),placeholder:"请输入上传文件字段名"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.accept!==void 0?(n(),i(d,{key:35,label:"文件类型"},{default:u(()=>[t(C,{modelValue:e.activeData.accept,"onUpdate:modelValue":l[36]||(l[36]=a=>e.activeData.accept=a),placeholder:"请选择文件类型",style:{width:"100%"},clearable:""},{default:u(()=>[t(k,{label:"图片",value:"image/*"}),t(k,{label:"视频",value:"video/*"}),t(k,{label:"音频",value:"audio/*"}),t(k,{label:"excel",value:".xls,.xlsx"}),t(k,{label:"word",value:".doc,.docx"}),t(k,{label:"pdf",value:".pdf"}),t(k,{label:"txt",value:".txt"})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.fileSize!==void 0?(n(),i(d,{key:36,label:"文件大小"},{default:u(()=>[t(r,{modelValue:e.activeData.fileSize,"onUpdate:modelValue":l[38]||(l[38]=a=>e.activeData.fileSize=a),modelModifiers:{number:!0},placeholder:"请输入文件大小"},{default:u(()=>[t(C,{slot:"append",modelValue:e.activeData.sizeUnit,"onUpdate:modelValue":l[37]||(l[37]=a=>e.activeData.sizeUnit=a),style:{width:"66px"}},{default:u(()=>[t(k,{label:"KB",value:"KB"}),t(k,{label:"MB",value:"MB"}),t(k,{label:"GB",value:"GB"})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.action!==void 0?(n(),i(d,{key:37,label:"上传地址"},{default:u(()=>[t(r,{modelValue:e.activeData.action,"onUpdate:modelValue":l[39]||(l[39]=a=>e.activeData.action=a),placeholder:"请输入上传地址",clearable:""},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["list-type"]!==void 0?(n(),i(d,{key:38,label:"列表类型"},{default:u(()=>[t(U,{modelValue:e.activeData["list-type"],"onUpdate:modelValue":l[40]||(l[40]=a=>e.activeData["list-type"]=a),size:"small"},{default:u(()=>[t(b,{label:"text"},{default:u(()=>l[91]||(l[91]=[s(" text ")])),_:1,__:[91]}),t(b,{label:"picture"},{default:u(()=>l[92]||(l[92]=[s(" picture ")])),_:1,__:[92]}),t(b,{label:"picture-card"},{default:u(()=>l[93]||(l[93]=[s(" picture-card ")])),_:1,__:[93]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.buttonText!==void 0?F((n(),i(d,{key:39,label:"按钮文字"},{default:u(()=>[t(r,{modelValue:e.activeData.buttonText,"onUpdate:modelValue":l[41]||(l[41]=a=>e.activeData.buttonText=a),placeholder:"请输入按钮文字"},null,8,["modelValue"])]),_:1},512)),[[j,e.activeData["list-type"]!=="picture-card"]]):o("",!0),e.activeData["range-separator"]!==void 0?(n(),i(d,{key:40,label:"分隔符"},{default:u(()=>[t(r,{modelValue:e.activeData["range-separator"],"onUpdate:modelValue":l[42]||(l[42]=a=>e.activeData["range-separator"]=a),placeholder:"请输入分隔符"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["picker-options"]!==void 0?(n(),i(d,{key:41,label:"时间段"},{default:u(()=>[t(r,{modelValue:e.activeData["picker-options"].selectableRange,"onUpdate:modelValue":l[43]||(l[43]=a=>e.activeData["picker-options"].selectableRange=a),placeholder:"请输入时间段"},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.format!==void 0?(n(),i(d,{key:42,label:"时间格式"},{default:u(()=>[t(r,{value:e.activeData.format,placeholder:"请输入时间格式",onInput:l[44]||(l[44]=a=>A(a))},null,8,["value"])]),_:1})):o("",!0),["el-checkbox-group","el-radio-group","el-select"].indexOf(e.activeData.tag)>-1?(n(),D(w,{key:43},[t(T,null,{default:u(()=>l[94]||(l[94]=[s("选项")])),_:1,__:[94]}),t(g(Fe),{list:e.activeData.options,animation:340,group:"selectItem",handle:".option-drag","item-key":"label"},{item:u(({element:a,index:V})=>[(n(),D("div",{key:V,class:"select-item"},[l[95]||(l[95]=x("div",{class:"select-line-icon option-drag"},[x("i",{class:"el-icon-s-operation"})],-1)),t(r,{modelValue:a.label,"onUpdate:modelValue":h=>a.label=h,placeholder:"选项名",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),t(r,{placeholder:"选项值",size:"small",value:a.value,onInput:h=>ke(a,h)},null,8,["value","onInput"]),x("div",{class:"close-btn select-line-icon",onClick:h=>e.activeData.options.splice(V,1)},[t(N,null,{default:u(()=>[t(Re)]),_:1})],8,Ge)]))]),_:1},8,["list"]),x("div",null,[t(R,{icon:"CirclePlus",style:{"margin-left":"8px","margin-top":"10px"},text:"",bg:"",type:"primary",onClick:ve},{default:u(()=>l[96]||(l[96]=[s(" 添加选项 ")])),_:1,__:[96]})]),t(T)],64)):o("",!0),["el-cascader"].indexOf(e.activeData.tag)>-1?(n(),D(w,{key:44},[t(T,null,{default:u(()=>l[97]||(l[97]=[s("选项")])),_:1,__:[97]}),t(d,{label:"数据类型"},{default:u(()=>[t(U,{modelValue:e.activeData.dataType,"onUpdate:modelValue":l[45]||(l[45]=a=>e.activeData.dataType=a),size:"small"},{default:u(()=>[t(b,{label:"dynamic"},{default:u(()=>l[98]||(l[98]=[s(" 动态数据 ")])),_:1,__:[98]}),t(b,{label:"static"},{default:u(()=>l[99]||(l[99]=[s(" 静态数据 ")])),_:1,__:[99]})]),_:1},8,["modelValue"])]),_:1}),e.activeData.dataType==="dynamic"?(n(),D(w,{key:0},[t(d,{label:"标签键名"},{default:u(()=>[t(r,{modelValue:e.activeData.labelKey,"onUpdate:modelValue":l[46]||(l[46]=a=>e.activeData.labelKey=a),placeholder:"请输入标签键名"},null,8,["modelValue"])]),_:1}),t(d,{label:"值键名"},{default:u(()=>[t(r,{modelValue:e.activeData.valueKey,"onUpdate:modelValue":l[47]||(l[47]=a=>e.activeData.valueKey=a),placeholder:"请输入值键名"},null,8,["modelValue"])]),_:1}),t(d,{label:"子级键名"},{default:u(()=>[t(r,{modelValue:e.activeData.childrenKey,"onUpdate:modelValue":l[48]||(l[48]=a=>e.activeData.childrenKey=a),placeholder:"请输入子级键名"},null,8,["modelValue"])]),_:1})],64)):o("",!0),e.activeData.dataType==="static"?(n(),i(E,{key:1,draggable:"",data:e.activeData.options,"node-key":"id","expand-on-click-node":!1,"render-content":Ve},null,8,["data"])):o("",!0),e.activeData.dataType==="static"?(n(),D("div",Je,[t(R,{icon:"CirclePlus",style:{"margin-left":"0","margin-top":"10px"},type:"primary",text:"",bg:"",onClick:be},{default:u(()=>l[100]||(l[100]=[s(" 添加父级 ")])),_:1,__:[100]})])):o("",!0),t(T)],64)):o("",!0),e.activeData.optionType!==void 0?(n(),i(d,{key:45,label:"选项样式"},{default:u(()=>[t(U,{modelValue:e.activeData.optionType,"onUpdate:modelValue":l[49]||(l[49]=a=>e.activeData.optionType=a)},{default:u(()=>[t(b,{label:"default"},{default:u(()=>l[101]||(l[101]=[s(" 默认 ")])),_:1,__:[101]}),t(b,{label:"button"},{default:u(()=>l[102]||(l[102]=[s(" 按钮 ")])),_:1,__:[102]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData["active-color"]!==void 0?(n(),i(d,{key:46,label:"开启颜色"},{default:u(()=>[t(Q,{modelValue:e.activeData["active-color"],"onUpdate:modelValue":l[50]||(l[50]=a=>e.activeData["active-color"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["inactive-color"]!==void 0?(n(),i(d,{key:47,label:"关闭颜色"},{default:u(()=>[t(Q,{modelValue:e.activeData["inactive-color"],"onUpdate:modelValue":l[51]||(l[51]=a=>e.activeData["inactive-color"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["allow-half"]!==void 0?(n(),i(d,{key:48,label:"允许半选"},{default:u(()=>[t(v,{modelValue:e.activeData["allow-half"],"onUpdate:modelValue":l[52]||(l[52]=a=>e.activeData["allow-half"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["show-text"]!==void 0?(n(),i(d,{key:49,label:"辅助文字"},{default:u(()=>[t(v,{modelValue:e.activeData["show-text"],"onUpdate:modelValue":l[53]||(l[53]=a=>e.activeData["show-text"]=a),onChange:Ce},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["show-score"]!==void 0?(n(),i(d,{key:50,label:"显示分数"},{default:u(()=>[t(v,{modelValue:e.activeData["show-score"],"onUpdate:modelValue":l[54]||(l[54]=a=>e.activeData["show-score"]=a),onChange:Te},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["show-stops"]!==void 0?(n(),i(d,{key:51,label:"显示间断点"},{default:u(()=>[t(v,{modelValue:e.activeData["show-stops"],"onUpdate:modelValue":l[55]||(l[55]=a=>e.activeData["show-stops"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.range!==void 0?(n(),i(d,{key:52,label:"范围选择"},{default:u(()=>[t(v,{modelValue:e.activeData.range,"onUpdate:modelValue":l[56]||(l[56]=a=>e.activeData.range=a),onChange:Ue},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.border!==void 0&&e.activeData.optionType==="default"?(n(),i(d,{key:53,label:"是否带边框"},{default:u(()=>[t(v,{modelValue:e.activeData.border,"onUpdate:modelValue":l[57]||(l[57]=a=>e.activeData.border=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-color-picker"?(n(),i(d,{key:54,label:"颜色格式"},{default:u(()=>[t(C,{modelValue:e.activeData["color-format"],"onUpdate:modelValue":l[58]||(l[58]=a=>e.activeData["color-format"]=a),placeholder:"请选择颜色格式",style:{width:"100%"},onChange:Ie},{default:u(()=>[(n(!0),D(w,null,I(g(de),(a,V)=>(n(),i(k,{key:V,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData.size!==void 0&&(e.activeData.optionType==="button"||e.activeData.border||e.activeData.tag==="el-color-picker")?(n(),i(d,{key:55,label:"选项尺寸"},{default:u(()=>[t(U,{modelValue:e.activeData.size,"onUpdate:modelValue":l[59]||(l[59]=a=>e.activeData.size=a)},{default:u(()=>[t(b,{label:"large"},{default:u(()=>l[103]||(l[103]=[s(" 较大 ")])),_:1,__:[103]}),t(b,{label:"default"},{default:u(()=>l[104]||(l[104]=[s(" 默认 ")])),_:1,__:[104]}),t(b,{label:"small"},{default:u(()=>l[105]||(l[105]=[s(" 较小 ")])),_:1,__:[105]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),e.activeData["show-word-limit"]!==void 0?(n(),i(d,{key:56,label:"输入统计"},{default:u(()=>[t(v,{modelValue:e.activeData["show-word-limit"],"onUpdate:modelValue":l[60]||(l[60]=a=>e.activeData["show-word-limit"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-input-number"?(n(),i(d,{key:57,label:"严格步数"},{default:u(()=>[t(v,{modelValue:e.activeData["step-strictly"],"onUpdate:modelValue":l[61]||(l[61]=a=>e.activeData["step-strictly"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-cascader"?(n(),i(d,{key:58,label:"是否多选"},{default:u(()=>[t(v,{modelValue:e.activeData.props.props.multiple,"onUpdate:modelValue":l[62]||(l[62]=a=>e.activeData.props.props.multiple=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-cascader"?(n(),i(d,{key:59,label:"展示全路径"},{default:u(()=>[t(v,{modelValue:e.activeData["show-all-levels"],"onUpdate:modelValue":l[63]||(l[63]=a=>e.activeData["show-all-levels"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-cascader"?(n(),i(d,{key:60,label:"可否筛选"},{default:u(()=>[t(v,{modelValue:e.activeData.filterable,"onUpdate:modelValue":l[64]||(l[64]=a=>e.activeData.filterable=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.clearable!==void 0?(n(),i(d,{key:61,label:"能否清空"},{default:u(()=>[t(v,{modelValue:e.activeData.clearable,"onUpdate:modelValue":l[65]||(l[65]=a=>e.activeData.clearable=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.showTip!==void 0?(n(),i(d,{key:62,label:"显示提示"},{default:u(()=>[t(v,{modelValue:e.activeData.showTip,"onUpdate:modelValue":l[66]||(l[66]=a=>e.activeData.showTip=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.multiple!==void 0?(n(),i(d,{key:63,label:"多选文件"},{default:u(()=>[t(v,{modelValue:e.activeData.multiple,"onUpdate:modelValue":l[67]||(l[67]=a=>e.activeData.multiple=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData["auto-upload"]!==void 0?(n(),i(d,{key:64,label:"自动上传"},{default:u(()=>[t(v,{modelValue:e.activeData["auto-upload"],"onUpdate:modelValue":l[68]||(l[68]=a=>e.activeData["auto-upload"]=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.readonly!==void 0?(n(),i(d,{key:65,label:"是否只读"},{default:u(()=>[t(v,{modelValue:e.activeData.readonly,"onUpdate:modelValue":l[69]||(l[69]=a=>e.activeData.readonly=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.disabled!==void 0?(n(),i(d,{key:66,label:"是否禁用"},{default:u(()=>[t(v,{modelValue:e.activeData.disabled,"onUpdate:modelValue":l[70]||(l[70]=a=>e.activeData.disabled=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-select"?(n(),i(d,{key:67,label:"是否可搜索"},{default:u(()=>[t(v,{modelValue:e.activeData.filterable,"onUpdate:modelValue":l[71]||(l[71]=a=>e.activeData.filterable=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.tag==="el-select"?(n(),i(d,{key:68,label:"是否多选"},{default:u(()=>[t(v,{modelValue:e.activeData.multiple,"onUpdate:modelValue":l[72]||(l[72]=a=>e.activeData.multiple=a),onChange:we},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.required!==void 0?(n(),i(d,{key:69,label:"是否必填"},{default:u(()=>[t(v,{modelValue:e.activeData.required,"onUpdate:modelValue":l[73]||(l[73]=a=>e.activeData.required=a)},null,8,["modelValue"])]),_:1})):o("",!0),e.activeData.layoutTree?(n(),D(w,{key:70},[t(T,null,{default:u(()=>l[106]||(l[106]=[s("布局结构树")])),_:1,__:[106]}),t(E,{data:[e.activeData],props:g(ie),"node-key":"renderKey","default-expand-all":"",draggable:""},{default:u(({node:a,data:V})=>[x("span",Ee,[t(J,{class:"node-icon","icon-class":V.tagIcon,style:{"margin-right":"5px"}},null,8,["icon-class"]),s(" "+K(a.label),1)])]),_:1},8,["data","props"])],64)):o("",!0),e.activeData.layout==="colFormItem"?(n(),D(w,{key:71},[t(T,null,{default:u(()=>l[107]||(l[107]=[s("正则校验")])),_:1,__:[107]}),(n(!0),D(w,null,I(e.activeData.regList,(a,V)=>(n(),D("div",{key:V,class:"reg-item"},[x("span",{class:"close-btn",onClick:h=>e.activeData.regList.splice(V,1)},[t(N,null,{default:u(()=>[t(Le)]),_:1})],8,Qe),t(d,{label:"表达式"},{default:u(()=>[t(r,{modelValue:a.pattern,"onUpdate:modelValue":h=>a.pattern=h,placeholder:"请输入正则"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t(d,{label:"错误提示",style:{"margin-bottom":"0"}},{default:u(()=>[t(r,{modelValue:a.message,"onUpdate:modelValue":h=>a.message=h,placeholder:"请输入错误提示"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]))),128)),x("div",null,[t(R,{icon:"CirclePlus",style:{"margin-left":"0","margin-top":"10px"},type:"primary",text:"",bg:"",onClick:se},{default:u(()=>l[108]||(l[108]=[s(" 添加规则 ")])),_:1,__:[108]})])],64)):o("",!0)]),_:1},512),[[j,g(z)==="field"&&e.showField]]),F(t(X,{"label-width":"90px","label-position":"top"},{default:u(()=>[t(d,{label:"表单名"},{default:u(()=>[t(r,{modelValue:e.formConf.formRef,"onUpdate:modelValue":l[74]||(l[74]=a=>e.formConf.formRef=a),placeholder:"请输入表单名（ref）"},null,8,["modelValue"])]),_:1}),t(d,{label:"表单模型"},{default:u(()=>[t(r,{modelValue:e.formConf.formModel,"onUpdate:modelValue":l[75]||(l[75]=a=>e.formConf.formModel=a),placeholder:"请输入数据模型"},null,8,["modelValue"])]),_:1}),t(d,{label:"校验模型"},{default:u(()=>[t(r,{modelValue:e.formConf.formRules,"onUpdate:modelValue":l[76]||(l[76]=a=>e.formConf.formRules=a),placeholder:"请输入校验模型"},null,8,["modelValue"])]),_:1}),t(d,{label:"表单尺寸"},{default:u(()=>[t(U,{modelValue:e.formConf.size,"onUpdate:modelValue":l[77]||(l[77]=a=>e.formConf.size=a)},{default:u(()=>[t(b,{label:"large",value:"较大"}),t(b,{label:"default",value:"默认"}),t(b,{label:"small",value:"较小"})]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"标签对齐"},{default:u(()=>[t(U,{modelValue:e.formConf.labelPosition,"onUpdate:modelValue":l[78]||(l[78]=a=>e.formConf.labelPosition=a)},{default:u(()=>[t(b,{label:"left",value:"左对齐"}),t(b,{label:"right",value:"右对齐"}),t(b,{label:"top",value:"顶部对齐"})]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"标签宽度"},{default:u(()=>[t(p,{modelValue:e.formConf.labelWidth,"onUpdate:modelValue":l[79]||(l[79]=a=>e.formConf.labelWidth=a),placeholder:"标签宽度"},null,8,["modelValue"])]),_:1}),t(d,{label:"栅格间隔"},{default:u(()=>[t(p,{modelValue:e.formConf.gutter,"onUpdate:modelValue":l[80]||(l[80]=a=>e.formConf.gutter=a),min:0,placeholder:"栅格间隔"},null,8,["modelValue"])]),_:1}),t(d,{label:"禁用表单"},{default:u(()=>[t(v,{modelValue:e.formConf.disabled,"onUpdate:modelValue":l[81]||(l[81]=a=>e.formConf.disabled=a)},null,8,["modelValue"])]),_:1}),t(d,{label:"表单按钮"},{default:u(()=>[t(v,{modelValue:e.formConf.formBtns,"onUpdate:modelValue":l[82]||(l[82]=a=>e.formConf.formBtns=a)},null,8,["modelValue"])]),_:1}),t(d,{label:"显示未选中组件边框"},{default:u(()=>[t(v,{modelValue:e.formConf.unFocusedComponentBorder,"onUpdate:modelValue":l[83]||(l[83]=a=>e.formConf.unFocusedComponentBorder=a)},null,8,["modelValue"])]),_:1})]),_:1},512),[[j,g(z)==="form"]])]),_:1})]),t(g(Ke),{modelValue:g(L),"onUpdate:modelValue":l[84]||(l[84]=a=>P(L)?L.value=a:null),current:e.activeData[g(q)],onSelect:ze},null,8,["modelValue","current"]),t(g(je),{modelValue:g(Y),"onUpdate:modelValue":l[85]||(l[85]=a=>P(Y)?Y.value=a:null),onCommit:ye},null,8,["modelValue"])])}}},Ze=Oe(Xe,[["__scopeId","data-v-e35a269a"]]),dl=Object.freeze(Object.defineProperty({__proto__:null,default:Ze},Symbol.toStringTag,{value:"Module"}));export{Ze as R,dl as a,tl as f,_ as i,ul as l,ee as s,nl as t};
