import{_ as te,z as ae,d as le,u as se,a as oe,r as v,B as ne,e as u,c as g,o as y,f as e,i as t,n as o,t as r,l as c,j as w,h as C,k as ie}from"./index-CdiCNU81.js";import{g as pe}from"./application-DkzhB5es.js";const re={class:"app-container"},ue={key:1},de={key:1},ce={key:1},_e={class:"asset-container"},me={class:"vulnerability-container"},fe=ae({name:"ApplicationDetail"}),ve=Object.assign(fe,{setup(ye){const{proxy:h}=le(),I=se(),T=oe(),{sdl_user_type:z,sdl_data_sensitivity:A,sdl_dev_type:N,sdl_security_level:B,sdl_business_domian:R,sdl_dev_language:U,sdl_business_importance:j,sdl_app_type:H,sdl_system_dependency:O,sdl_access_path:E}=h.useDict("sdl_user_type","sdl_data_sensitivity","sdl_dev_type","sdl_security_level","sdl_business_domian","sdl_dev_language","sdl_business_importance","sdl_app_type","sdl_system_dependency","sdl_access_path"),n=v({}),D=v("basic"),L=v("应用详情"),Q=v([{ip:"*************",type:"内网",port:"80,443,22",service:"HTTP,HTTPS,SSH",status:"在线",location:"北京数据中心",lastScan:"2025-01-05 14:30"},{ip:"*********",type:"内网",port:"3306,6379",service:"MySQL,Redis",status:"在线",location:"北京数据中心",lastScan:"2025-01-05 14:30"},{ip:"************",type:"外网",port:"80,443",service:"HTTP,HTTPS",status:"在线",location:"阿里云华北",lastScan:"2025-01-05 14:30"}]),$=v([{domain:"app.example.com",type:"A",value:"************",ttl:"300",status:"正常",registrar:"阿里云",expireDate:"2025-12-31"},{domain:"api.example.com",type:"CNAME",value:"app.example.com",ttl:"600",status:"正常",registrar:"阿里云",expireDate:"2025-12-31"},{domain:"test.example.com",type:"A",value:"*************",ttl:"300",status:"正常",registrar:"腾讯云",expireDate:"2025-06-30"}]),F=v([{name:"sdl-platform-backend",url:"https://git.example.com/sdl/sdl-platform-backend.git",language:"Java",branch:"main",lastCommit:"2025-01-05 16:45",contributors:8,status:"活跃"},{name:"sdl-platform-frontend",url:"https://git.example.com/sdl/sdl-platform-frontend.git",language:"Vue.js",branch:"main",lastCommit:"2025-01-05 15:20",contributors:5,status:"活跃"},{name:"sdl-platform-config",url:"https://git.example.com/sdl/sdl-platform-config.git",language:"YAML",branch:"main",lastCommit:"2025-01-03 10:15",contributors:3,status:"维护"}]);v({critical:2,high:5,medium:12,low:8});const X=v([{id:"VUL-2025-001",title:"SQL注入漏洞",severity:"严重",type:"SQL注入",component:"user-service",cvss:"9.8",status:"未修复",discoveredDate:"2025-01-03"},{id:"VUL-2025-002",title:"XSS跨站脚本攻击",severity:"高危",type:"XSS",component:"web-ui",cvss:"7.5",status:"修复中",discoveredDate:"2025-01-02"},{id:"VUL-2025-003",title:"敏感信息泄露",severity:"中危",type:"信息泄露",component:"api-gateway",cvss:"5.3",status:"已修复",discoveredDate:"2024-12-28"},{id:"VUL-2025-004",title:"弱密码策略",severity:"低危",type:"配置错误",component:"auth-service",cvss:"3.1",status:"未修复",discoveredDate:"2024-12-25"}]);function P(i){return i?i.split(",").map(a=>a.trim()).filter(a=>a):[]}function V(i){return i?i.split(",").map(a=>a.trim()).filter(a=>a):[]}function M(i){return i?i.split(",").map(a=>a.trim()).filter(a=>a):[]}function q(i){return{内网:"info",外网:"warning",VPN:"success"}[i]||"info"}function k(i){return{在线:"success",离线:"danger",正常:"success",异常:"danger",活跃:"success",维护:"warning",废弃:"info"}[i]||"info"}function J(i){return{严重:"danger",高危:"warning",中危:"primary",低危:"success"}[i]||"info"}function Y(i){const a=parseFloat(i);return a>=9?"danger":a>=7?"warning":a>=4?"primary":"success"}function G(i){return{未修复:"danger",修复中:"warning",已修复:"success",已忽略:"info"}[i]||"info"}function K(){T.back()}function W(){const i=I.query.id;i?pe(i).then(a=>{n.value=a.data,L.value=`${n.value.appName}`}).catch(()=>{h.$modal.msgError("获取应用详情失败"),T.back()}):(h.$modal.msgError("缺少应用ID参数"),T.back())}return ne(()=>{W()}),(i,a)=>{const Z=u("el-page-header"),p=u("el-descriptions-item"),d=u("dict-tag"),x=u("el-descriptions"),f=u("el-card"),S=u("el-tab-pane"),l=u("el-table-column"),_=u("el-tag"),m=u("el-button"),b=u("el-table"),ee=u("el-tabs");return y(),g("div",re,[e(Z,{onBack:K,content:L.value},null,8,["content"]),e(f,{class:"box-card",style:{"margin-top":"20px"}},{default:t(()=>[e(ee,{modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=s=>D.value=s)},{default:t(()=>[e(S,{label:"应用信息",name:"basic"},{default:t(()=>[e(f,{class:"asset-card",header:"基本信息"},{default:t(()=>[e(x,{column:3,border:""},{default:t(()=>[e(p,{label:"应用名称"},{default:t(()=>[o(r(n.value.appName||"-"),1)]),_:1}),e(p,{label:"所属部门"},{default:t(()=>[o(r(n.value.department||"-"),1)]),_:1}),e(p,{label:"责任人"},{default:t(()=>[o(r(n.value.responsiblePerson||"-"),1)]),_:1}),e(p,{label:"应用类型"},{default:t(()=>[e(d,{options:c(H),value:n.value.appType},null,8,["options","value"])]),_:1}),e(p,{label:"业务领域"},{default:t(()=>[e(d,{options:c(R),value:n.value.businessDomain},null,8,["options","value"])]),_:1}),e(p,{label:"安全代表"},{default:t(()=>[o(r(n.value.securityRepresentative||"-"),1)]),_:1}),e(p,{label:"开发类型"},{default:t(()=>[e(d,{options:c(N),value:n.value.developmentType},null,8,["options","value"])]),_:1}),e(p,{label:"应用成员",span:2},{default:t(()=>[o(r(n.value.appMenbers||"-"),1)]),_:1})]),_:1})]),_:1}),e(f,{class:"asset-card",header:"定级信息",style:{"margin-top":"20px"}},{default:t(()=>[e(x,{column:3,border:""},{default:t(()=>[e(p,{label:"用户类型"},{default:t(()=>[P(n.value.userType).length>0?(y(),w(d,{key:0,options:c(z),value:P(n.value.userType)},null,8,["options","value"])):(y(),g("span",ue,"-"))]),_:1}),e(p,{label:"数据敏感度"},{default:t(()=>[e(d,{options:c(A),value:n.value.dataSensitivity},null,8,["options","value"])]),_:1}),e(p,{label:"业务重要性"},{default:t(()=>[e(d,{options:c(j),value:n.value.businessImportance},null,8,["options","value"])]),_:1}),e(p,{label:"系统依赖性"},{default:t(()=>[e(d,{options:c(O),value:n.value.systemDependency},null,8,["options","value"])]),_:1}),e(p,{label:"安全等级"},{default:t(()=>[e(d,{options:c(B),value:n.value.securityLevel},null,8,["options","value"])]),_:1}),e(p,{label:"访问路径",span:1},{default:t(()=>[M(n.value.accessPath).length>0?(y(),w(d,{key:0,options:c(E),value:M(n.value.accessPath)},null,8,["options","value"])):(y(),g("span",de,"-"))]),_:1})]),_:1})]),_:1}),e(f,{class:"asset-card",header:"其它信息",style:{"margin-top":"20px"}},{default:t(()=>[e(x,{column:3,border:""},{default:t(()=>[e(p,{label:"开发语言",span:3},{default:t(()=>[V(n.value.developmentLanguage).length>0?(y(),w(d,{key:0,options:c(U),value:V(n.value.developmentLanguage)},null,8,["options","value"])):(y(),g("span",ce,"-"))]),_:1}),e(p,{label:"RTO (恢复时间目标)"},{default:t(()=>[o(r(n.value.recoveryTimeObjective||"-"),1)]),_:1}),e(p,{label:"RPO (恢复时间点目标)"},{default:t(()=>[o(r(n.value.recoveryPointObjective||"-"),1)]),_:1}),e(p,{label:"创建时间"},{default:t(()=>[o(r(i.parseTime(n.value.createTime)),1)]),_:1}),e(p,{label:"更新时间"},{default:t(()=>[o(r(i.parseTime(n.value.updateTime)),1)]),_:1}),e(p,{label:"备注",span:3},{default:t(()=>[o(r(n.value.remark||"-"),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(S,{label:"资产信息",name:"assets"},{default:t(()=>[C("div",_e,[e(f,{class:"asset-card",header:"IP资产信息"},{default:t(()=>[e(b,{data:Q.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(l,{prop:"ip",label:"IP地址",width:"150"}),e(l,{prop:"type",label:"IP类型",width:"100"},{default:t(s=>[e(_,{type:q(s.row.type)},{default:t(()=>[o(r(s.row.type),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"port",label:"端口",width:"120"}),e(l,{prop:"service",label:"服务",width:"150"}),e(l,{prop:"status",label:"状态",width:"100"},{default:t(s=>[e(_,{type:k(s.row.status)},{default:t(()=>[o(r(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"location",label:"位置"}),e(l,{prop:"lastScan",label:"最后扫描时间",width:"160"}),e(l,{label:"操作",width:"120"},{default:t(s=>[e(m,{link:"",type:"primary",size:"small"},{default:t(()=>a[1]||(a[1]=[o("详情")])),_:1,__:[1]}),e(m,{link:"",type:"warning",size:"small"},{default:t(()=>a[2]||(a[2]=[o("扫描")])),_:1,__:[2]})]),_:1})]),_:1},8,["data"])]),_:1}),e(f,{class:"asset-card",header:"域名资产信息",style:{"margin-top":"20px"}},{default:t(()=>[e(b,{data:$.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(l,{prop:"domain",label:"域名"}),e(l,{prop:"type",label:"记录类型",width:"100"},{default:t(s=>[e(_,null,{default:t(()=>[o(r(s.row.type),1)]),_:2},1024)]),_:1}),e(l,{prop:"value",label:"解析值"}),e(l,{prop:"ttl",label:"TTL",width:"80"}),e(l,{prop:"status",label:"状态",width:"100"},{default:t(s=>[e(_,{type:k(s.row.status)},{default:t(()=>[o(r(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"registrar",label:"注册商",width:"120"}),e(l,{prop:"expireDate",label:"过期时间",width:"120"}),e(l,{label:"操作",width:"120"},{default:t(s=>[e(m,{link:"",type:"primary",size:"small"},{default:t(()=>a[3]||(a[3]=[o("详情")])),_:1,__:[3]}),e(m,{link:"",type:"warning",size:"small"},{default:t(()=>a[4]||(a[4]=[o("监控")])),_:1,__:[4]})]),_:1})]),_:1},8,["data"])]),_:1}),e(f,{class:"asset-card",header:"代码仓库信息",style:{"margin-top":"20px"}},{default:t(()=>[e(b,{data:F.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(l,{prop:"name",label:"仓库名称"}),e(l,{prop:"url",label:"仓库地址","show-overflow-tooltip":""}),e(l,{prop:"language",label:"主要语言",width:"120"},{default:t(s=>[e(_,null,{default:t(()=>[o(r(s.row.language),1)]),_:2},1024)]),_:1}),e(l,{prop:"branch",label:"主分支",width:"100"}),e(l,{prop:"lastCommit",label:"最后提交",width:"160"}),e(l,{prop:"contributors",label:"贡献者数",width:"100"}),e(l,{prop:"status",label:"状态",width:"100"},{default:t(s=>[e(_,{type:k(s.row.status)},{default:t(()=>[o(r(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(l,{label:"操作",width:"120"},{default:t(s=>[e(m,{link:"",type:"primary",size:"small"},{default:t(()=>a[5]||(a[5]=[o("访问")])),_:1,__:[5]}),e(m,{link:"",type:"warning",size:"small"},{default:t(()=>a[6]||(a[6]=[o("扫描")])),_:1,__:[6]})]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1}),e(S,{label:"安全漏洞",name:"vulnerabilities"},{default:t(()=>[C("div",me,[e(f,{class:"vulnerability-card",header:"漏洞详情"},{default:t(()=>[e(b,{data:X.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(l,{prop:"id",label:"漏洞ID",width:"120"}),e(l,{prop:"title",label:"漏洞标题","show-overflow-tooltip":""}),e(l,{prop:"severity",label:"严重程度",width:"100"},{default:t(s=>[e(_,{type:J(s.row.severity)},{default:t(()=>[o(r(s.row.severity),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"type",label:"漏洞类型",width:"120"}),e(l,{prop:"component",label:"影响组件",width:"150"}),e(l,{prop:"cvss",label:"CVSS评分",width:"100"},{default:t(s=>[e(_,{type:Y(s.row.cvss)},{default:t(()=>[o(r(s.row.cvss),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"status",label:"修复状态",width:"100"},{default:t(s=>[e(_,{type:G(s.row.status)},{default:t(()=>[o(r(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(l,{prop:"discoveredDate",label:"发现时间",width:"120"}),e(l,{label:"操作",width:"150"},{default:t(s=>[e(m,{link:"",type:"primary",size:"small"},{default:t(()=>a[7]||(a[7]=[o("详情")])),_:1,__:[7]}),s.row.status==="未修复"?(y(),w(m,{key:0,link:"",type:"success",size:"small"},{default:t(()=>a[8]||(a[8]=[o("修复")])),_:1,__:[8]})):ie("",!0),e(m,{link:"",type:"warning",size:"small"},{default:t(()=>a[9]||(a[9]=[o("忽略")])),_:1,__:[9]})]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}}),we=te(ve,[["__scopeId","data-v-2028f1c9"]]);export{we as default};
