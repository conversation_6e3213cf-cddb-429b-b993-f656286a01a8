import{z as _e,d as je,a as be,r as b,A as ge,I as ve,e as i,F as O,c as C,o as p,G as g,f as e,H as W,i as t,m as z,l as o,J as U,K as D,j as m,n as c,h as ye}from"./index-CdiCNU81.js";import{l as Ve,g as we,d as Se,u as Ce,a as ke}from"./project-Dl87E-1X.js";const Te={class:"app-container"},Ue={class:"dialog-footer"},De=_e({name:"Project"}),Ie=Object.assign(De,{setup(Ne){const{proxy:v}=je(),X=be(),{sdl_project_scope:x,sdl_project_status:M,sdl_project_type:I}=v.useDict("sdl_project_scope","sdl_project_status","sdl_project_type"),E=b([]),V=b(!1),P=b(!0),R=b(!0),h=b([]),Y=b(!0),Q=b(!0),q=b(0),L=b(""),$=b([]),Z=ge({form:{},queryParams:{pageNum:1,pageSize:10,projectName:null,projectStatus:null,projectManager:null,projectScope:null,belongDepartment:null,projectLabel:null,projectType:null,startCreateTime:null,endCreateTime:null},rules:{projectId:[{required:!0,message:"项目ID不能为空",trigger:"blur"}],projectCode:[{required:!0,message:"项目编号不能为空",trigger:"blur"}],projectName:[{required:!0,message:"项目名称不能为空",trigger:"blur"}]}}),{queryParams:r,form:n,rules:ee}=ve(Z);function le(d){d&&d.length===2?(r.value.startCreateTime=d[0],r.value.endCreateTime=d[1]):(r.value.startCreateTime=null,r.value.endCreateTime=null)}function te(d){const l=d.projectId;X.push({path:"/business/project/detail",query:{id:l}})}function w(){P.value=!0,Ve(r.value).then(d=>{E.value=d.rows,q.value=d.total,P.value=!1})}function ae(){V.value=!1,B()}function B(){n.value={projectId:null,projectCode:null,projectName:null,projectStatus:null,projectManager:null,startTime:null,projectScope:null,belongDepartment:null,projectLabel:null,projectType:null,projectMembers:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},v.resetForm("projectRef")}function N(){r.value.pageNum=1,w()}function oe(){$.value=[],v.resetForm("queryRef"),N()}function ne(d){h.value=d.map(l=>l.projectId),Y.value=d.length!=1,Q.value=!d.length}function re(){B(),V.value=!0,L.value="添加项目"}function A(d){B();const l=d.projectId||h.value;we(l).then(f=>{n.value=f.data,V.value=!0,L.value="修改项目"})}function ue(){v.$refs.projectRef.validate(d=>{d&&(n.value.projectId!=null?Ce(n.value).then(l=>{v.$modal.msgSuccess("修改成功"),V.value=!1,w()}):ke(n.value).then(l=>{v.$modal.msgSuccess("新增成功"),V.value=!1,w()}))})}function G(d){const l=d.projectId||h.value;v.$modal.confirm('是否确认删除项目编号为"'+l+'"的数据项？').then(function(){return Se(l)}).then(()=>{w(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function pe(){v.download("business/project/export",{...r.value},`project_${new Date().getTime()}.xlsx`)}return w(),(d,l)=>{const f=i("el-input"),s=i("el-form-item"),u=i("el-col"),k=i("el-option"),T=i("el-select"),j=i("el-row"),de=i("el-date-picker"),_=i("el-button"),H=i("el-form"),se=i("right-toolbar"),y=i("el-table-column"),F=i("dict-tag"),ie=i("el-table"),me=i("pagination"),K=i("el-divider"),ce=i("el-dialog"),S=O("hasPermi"),fe=O("loading");return p(),C("div",Te,[g(e(H,{model:o(r),ref:"queryRef","label-width":"80px"},{default:t(()=>[e(j,{gutter:20},{default:t(()=>[e(u,{span:6},{default:t(()=>[e(s,{label:"项目名称",prop:"projectName"},{default:t(()=>[e(f,{modelValue:o(r).projectName,"onUpdate:modelValue":l[0]||(l[0]=a=>o(r).projectName=a),placeholder:"请输入项目名称",clearable:"",onKeyup:z(N,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{label:"项目状态",prop:"projectStatus"},{default:t(()=>[e(T,{modelValue:o(r).projectStatus,"onUpdate:modelValue":l[1]||(l[1]=a=>o(r).projectStatus=a),placeholder:"请选择项目状态",clearable:"",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(M),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{label:"项目经理",prop:"projectManager"},{default:t(()=>[e(f,{modelValue:o(r).projectManager,"onUpdate:modelValue":l[2]||(l[2]=a=>o(r).projectManager=a),placeholder:"请输入项目经理",clearable:"",onKeyup:z(N,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{label:"项目类型",prop:"projectType"},{default:t(()=>[e(T,{modelValue:o(r).projectType,"onUpdate:modelValue":l[3]||(l[3]=a=>o(r).projectType=a),placeholder:"请选择项目类型",clearable:"",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(I),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(j,{gutter:20},{default:t(()=>[e(u,{span:6},{default:t(()=>[e(s,{label:"立项范围",prop:"projectScope"},{default:t(()=>[e(T,{modelValue:o(r).projectScope,"onUpdate:modelValue":l[4]||(l[4]=a=>o(r).projectScope=a),placeholder:"请选择立项范围",clearable:"",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(x),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{label:"归属部门",prop:"belongDepartment"},{default:t(()=>[e(f,{modelValue:o(r).belongDepartment,"onUpdate:modelValue":l[5]||(l[5]=a=>o(r).belongDepartment=a),placeholder:"请输入归属部门",clearable:"",onKeyup:z(N,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{label:"创建时间",prop:"dateRange"},{default:t(()=>[e(de,{modelValue:$.value,"onUpdate:modelValue":l[6]||(l[6]=a=>$.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",onChange:le},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(s,{style:{"margin-right":"5px"}},{default:t(()=>[e(_,{type:"primary",icon:"Search",onClick:N},{default:t(()=>l[22]||(l[22]=[c("搜索")])),_:1,__:[22]}),e(_,{icon:"Refresh",onClick:oe},{default:t(()=>l[23]||(l[23]=[c("重置")])),_:1,__:[23]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),[[W,R.value]]),e(j,{gutter:10,class:"mb8"},{default:t(()=>[e(u,{span:1.5},{default:t(()=>[g((p(),m(_,{type:"primary",plain:"",icon:"Plus",onClick:re},{default:t(()=>l[24]||(l[24]=[c("新增")])),_:1,__:[24]})),[[S,["business:project:add"]]])]),_:1}),e(u,{span:1.5},{default:t(()=>[g((p(),m(_,{type:"success",plain:"",icon:"Edit",disabled:Y.value,onClick:A},{default:t(()=>l[25]||(l[25]=[c("修改")])),_:1,__:[25]},8,["disabled"])),[[S,["business:project:edit"]]])]),_:1}),e(u,{span:1.5},{default:t(()=>[g((p(),m(_,{type:"danger",plain:"",icon:"Delete",disabled:Q.value,onClick:G},{default:t(()=>l[26]||(l[26]=[c("删除")])),_:1,__:[26]},8,["disabled"])),[[S,["business:project:remove"]]])]),_:1}),e(u,{span:1.5},{default:t(()=>[g((p(),m(_,{type:"warning",plain:"",icon:"Download",onClick:pe},{default:t(()=>l[27]||(l[27]=[c("导出")])),_:1,__:[27]})),[[S,["business:project:export"]]])]),_:1}),e(se,{showSearch:R.value,"onUpdate:showSearch":l[7]||(l[7]=a=>R.value=a),onQueryTable:w},null,8,["showSearch"])]),_:1}),g((p(),m(ie,{data:E.value,onSelectionChange:ne,fit:"",style:{width:"100%"}},{default:t(()=>[e(y,{type:"selection",width:"55",align:"center"}),e(y,{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":""}),e(y,{label:"项目状态",align:"center",prop:"projectStatus"},{default:t(a=>[e(F,{options:o(M),value:a.row.projectStatus},null,8,["options","value"])]),_:1}),e(y,{label:"项目经理",align:"center",prop:"projectManager"}),e(y,{label:"立项范围",align:"center",prop:"projectScope"},{default:t(a=>[e(F,{options:o(x),value:a.row.projectScope},null,8,["options","value"])]),_:1}),e(y,{label:"归属部门",align:"center",prop:"belongDepartment"}),e(y,{label:"项目类型",align:"center",prop:"projectType"},{default:t(a=>[e(F,{options:o(I),value:a.row.projectType},null,8,["options","value"])]),_:1}),e(y,{label:"项目标签",align:"center",prop:"projectLabel","show-overflow-tooltip":""}),e(y,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},{default:t(a=>[g((p(),m(_,{link:"",type:"primary",icon:"View",onClick:J=>te(a.row)},{default:t(()=>l[28]||(l[28]=[c("查看")])),_:2,__:[28]},1032,["onClick"])),[[S,["business:project:query"]]]),g((p(),m(_,{link:"",type:"primary",icon:"Edit",onClick:J=>A(a.row)},{default:t(()=>l[29]||(l[29]=[c("修改")])),_:2,__:[29]},1032,["onClick"])),[[S,["business:project:edit"]]]),g((p(),m(_,{link:"",type:"primary",icon:"Delete",onClick:J=>G(a.row)},{default:t(()=>l[30]||(l[30]=[c("删除")])),_:2,__:[30]},1032,["onClick"])),[[S,["business:project:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,P.value]]),g(e(me,{total:q.value,page:o(r).pageNum,"onUpdate:page":l[8]||(l[8]=a=>o(r).pageNum=a),limit:o(r).pageSize,"onUpdate:limit":l[9]||(l[9]=a=>o(r).pageSize=a),onPagination:w},null,8,["total","page","limit"]),[[W,q.value>0]]),e(ce,{title:L.value,modelValue:V.value,"onUpdate:modelValue":l[21]||(l[21]=a=>V.value=a),width:"800px","append-to-body":""},{footer:t(()=>[ye("div",Ue,[e(_,{type:"primary",onClick:ue},{default:t(()=>l[34]||(l[34]=[c("确 定")])),_:1,__:[34]}),e(_,{onClick:ae},{default:t(()=>l[35]||(l[35]=[c("取 消")])),_:1,__:[35]})])]),default:t(()=>[e(H,{ref:"projectRef",model:o(n),rules:o(ee),"label-width":"100px"},{default:t(()=>[e(K,{"content-position":"left"},{default:t(()=>l[31]||(l[31]=[c("基本信息")])),_:1,__:[31]}),e(j,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(s,{label:"项目编号",prop:"projectCode"},{default:t(()=>[e(f,{modelValue:o(n).projectCode,"onUpdate:modelValue":l[10]||(l[10]=a=>o(n).projectCode=a),placeholder:"请输入项目编号"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(s,{label:"项目名称",prop:"projectName"},{default:t(()=>[e(f,{modelValue:o(n).projectName,"onUpdate:modelValue":l[11]||(l[11]=a=>o(n).projectName=a),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(j,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(s,{label:"项目状态",prop:"projectStatus"},{default:t(()=>[e(T,{modelValue:o(n).projectStatus,"onUpdate:modelValue":l[12]||(l[12]=a=>o(n).projectStatus=a),placeholder:"请选择项目状态",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(M),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(s,{label:"项目类型",prop:"projectType"},{default:t(()=>[e(T,{modelValue:o(n).projectType,"onUpdate:modelValue":l[13]||(l[13]=a=>o(n).projectType=a),placeholder:"请选择项目类型",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(I),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(j,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(s,{label:"开始时间",prop:"startTime"},{default:t(()=>[e(f,{modelValue:o(n).startTime,"onUpdate:modelValue":l[14]||(l[14]=a=>o(n).startTime=a),placeholder:"请输入开始时间"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(s,{label:"立项范围",prop:"projectScope"},{default:t(()=>[e(T,{modelValue:o(n).projectScope,"onUpdate:modelValue":l[15]||(l[15]=a=>o(n).projectScope=a),placeholder:"请选择立项范围",style:{width:"100%"}},{default:t(()=>[(p(!0),C(U,null,D(o(x),a=>(p(),m(k,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(K,{"content-position":"left"},{default:t(()=>l[32]||(l[32]=[c("组织信息")])),_:1,__:[32]}),e(j,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(s,{label:"项目经理",prop:"projectManager"},{default:t(()=>[e(f,{modelValue:o(n).projectManager,"onUpdate:modelValue":l[16]||(l[16]=a=>o(n).projectManager=a),placeholder:"请输入项目经理"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(s,{label:"归属部门",prop:"belongDepartment"},{default:t(()=>[e(f,{modelValue:o(n).belongDepartment,"onUpdate:modelValue":l[17]||(l[17]=a=>o(n).belongDepartment=a),placeholder:"请输入归属部门"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(j,{gutter:20},{default:t(()=>[e(u,{span:24},{default:t(()=>[e(s,{label:"项目成员",prop:"projectMembers"},{default:t(()=>[e(f,{modelValue:o(n).projectMembers,"onUpdate:modelValue":l[18]||(l[18]=a=>o(n).projectMembers=a),rows:3,placeholder:"请输入项目成员，多个成员用逗号分隔"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(K,{"content-position":"left"},{default:t(()=>l[33]||(l[33]=[c("其他信息")])),_:1,__:[33]}),e(j,{gutter:20},{default:t(()=>[e(u,{span:24},{default:t(()=>[e(s,{label:"项目标签",prop:"projectLabel"},{default:t(()=>[e(f,{modelValue:o(n).projectLabel,"onUpdate:modelValue":l[19]||(l[19]=a=>o(n).projectLabel=a),placeholder:"请输入项目标签，多个标签用逗号分隔"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(j,{gutter:20},{default:t(()=>[e(u,{span:24},{default:t(()=>[e(s,{label:"备注",prop:"remark"},{default:t(()=>[e(f,{modelValue:o(n).remark,"onUpdate:modelValue":l[20]||(l[20]=a=>o(n).remark=a),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ie as default};
