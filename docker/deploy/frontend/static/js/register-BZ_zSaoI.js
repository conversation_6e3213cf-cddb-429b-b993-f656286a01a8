import{_ as P,a as R,d as U,r as f,e as i,c as w,o as _,f as s,h as u,i as r,j as z,k as B,t as K,l as o,m as y,p as N,n as T,q as I,v as M,E as S}from"./index-CdiCNU81.js";const $={class:"register"},D={class:"title"},L={class:"register-code"},j=["src"],A={key:0},F={key:1},H={style:{float:"right"}},G={__name:"register",setup(J){const V="SDL平台",x=R(),{proxy:b}=U(),t=f({username:"",password:"",confirmPassword:"",code:"",uuid:""}),k={username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,e,a)=>{t.value.password!==e?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},h=f(""),c=f(!1),p=f(!0);function m(){b.$refs.registerRef.validate(l=>{l&&(c.value=!0,M(t.value).then(e=>{const a=t.value.username;S.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{x.push("/login")}).catch(()=>{})}).catch(()=>{c.value=!1,p&&v()}))})}function v(){I().then(l=>{p.value=l.captchaEnabled===void 0?!0:l.captchaEnabled,p.value&&(h.value="data:image/gif;base64,"+l.img,t.value.uuid=l.uuid)})}return v(),(l,e)=>{const a=i("svg-icon"),g=i("el-input"),d=i("el-form-item"),C=i("el-button"),q=i("router-link"),E=i("el-form");return _(),w("div",$,[s(E,{ref:"registerRef",model:o(t),rules:k,class:"register-form"},{default:r(()=>[u("h3",D,K(o(V)),1),s(d,{prop:"username"},{default:r(()=>[s(g,{modelValue:o(t).username,"onUpdate:modelValue":e[0]||(e[0]=n=>o(t).username=n),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:r(()=>[s(a,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(d,{prop:"password"},{default:r(()=>[s(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(t).password=n),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(d,{prop:"confirmPassword"},{default:r(()=>[s(g,{modelValue:o(t).confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).confirmPassword=n),type:"password",size:"large","auto-complete":"off",placeholder:"确认密码",onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),o(p)?(_(),z(d,{key:0,prop:"code"},{default:r(()=>[s(g,{size:"large",modelValue:o(t).code,"onUpdate:modelValue":e[3]||(e[3]=n=>o(t).code=n),"auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),u("div",L,[u("img",{src:o(h),onClick:v,class:"register-code-img"},null,8,j)])]),_:1})):B("",!0),s(d,{style:{width:"100%"}},{default:r(()=>[s(C,{loading:o(c),size:"large",type:"primary",style:{width:"100%"},onClick:N(m,["prevent"])},{default:r(()=>[o(c)?(_(),w("span",F,"注 册 中...")):(_(),w("span",A,"注 册"))]),_:1},8,["loading"]),u("div",H,[s(q,{class:"link-type",to:"/login"},{default:r(()=>e[4]||(e[4]=[T("使用已有账户登录")])),_:1,__:[4]})])]),_:1})]),_:1},8,["model"]),e[5]||(e[5]=u("div",{class:"el-register-footer"},[u("span",null,"Copyright © 2018-2025 ruoyi.vip All Rights Reserved.")],-1))])}}},W=P(G,[["__scopeId","data-v-98f7f576"]]);export{W as default};
