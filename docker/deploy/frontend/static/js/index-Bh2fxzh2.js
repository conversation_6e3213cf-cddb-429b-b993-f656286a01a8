import{z as ce,d as _e,r as g,A as ye,I as ve,e as a,F as B,c as R,o as d,G as c,f as l,H as E,l as t,i as n,m as L,J as Q,K as j,j as _,C as I,n as s,h as A,t as M,U as be,V as Ve,W as we,X as Ce,Y as ke,Z as he}from"./index-CdiCNU81.js";const xe={class:"app-container"},Ne={class:"dialog-footer"},Se=ce({name:"Config"}),Ke=Object.assign(Se,{setup(Te){const{proxy:p}=_e(),{sys_yes_no:N}=p.useDict("sys_yes_no"),$=g([]),v=g(!1),S=g(!0),h=g(!0),T=g([]),q=g(!0),P=g(!0),U=g(0),K=g(""),C=g([]),G=ye({form:{},queryParams:{pageNum:1,pageSize:10,configName:void 0,configKey:void 0,configType:void 0},rules:{configName:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],configKey:[{required:!0,message:"参数键名不能为空",trigger:"blur"}],configValue:[{required:!0,message:"参数键值不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:H}=ve(G);function V(){S.value=!0,be(p.addDateRange(u.value,C.value)).then(r=>{$.value=r.rows,U.value=r.total,S.value=!1})}function J(){v.value=!1,D()}function D(){i.value={configId:void 0,configName:void 0,configKey:void 0,configValue:void 0,configType:"Y",remark:void 0},p.resetForm("configRef")}function x(){u.value.pageNum=1,V()}function O(){C.value=[],p.resetForm("queryRef"),x()}function W(r){T.value=r.map(e=>e.configId),q.value=r.length!=1,P.value=!r.length}function X(){D(),v.value=!0,K.value="添加参数"}function Y(r){D();const e=r.configId||T.value;Ve(e).then(b=>{i.value=b.data,v.value=!0,K.value="修改参数"})}function Z(){p.$refs.configRef.validate(r=>{r&&(i.value.configId!=null?ke(i.value).then(e=>{p.$modal.msgSuccess("修改成功"),v.value=!1,V()}):he(i.value).then(e=>{p.$modal.msgSuccess("新增成功"),v.value=!1,V()}))})}function F(r){const e=r.configId||T.value;p.$modal.confirm('是否确认删除参数编号为"'+e+'"的数据项？').then(function(){return we(e)}).then(()=>{V(),p.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){p.download("system/config/export",{...u.value},`config_${new Date().getTime()}.xlsx`)}function le(){Ce().then(()=>{p.$modal.msgSuccess("刷新缓存成功")})}return V(),(r,e)=>{const b=a("el-input"),m=a("el-form-item"),oe=a("el-option"),ne=a("el-select"),te=a("el-date-picker"),f=a("el-button"),z=a("el-form"),k=a("el-col"),ae=a("right-toolbar"),ie=a("el-row"),y=a("el-table-column"),ue=a("dict-tag"),re=a("el-table"),de=a("pagination"),se=a("el-radio"),pe=a("el-radio-group"),fe=a("el-dialog"),w=B("hasPermi"),me=B("loading");return d(),R("div",xe,[c(l(z,{model:t(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[l(m,{label:"参数名称",prop:"configName"},{default:n(()=>[l(b,{modelValue:t(u).configName,"onUpdate:modelValue":e[0]||(e[0]=o=>t(u).configName=o),placeholder:"请输入参数名称",clearable:"",style:{width:"240px"},onKeyup:L(x,["enter"])},null,8,["modelValue"])]),_:1}),l(m,{label:"参数键名",prop:"configKey"},{default:n(()=>[l(b,{modelValue:t(u).configKey,"onUpdate:modelValue":e[1]||(e[1]=o=>t(u).configKey=o),placeholder:"请输入参数键名",clearable:"",style:{width:"240px"},onKeyup:L(x,["enter"])},null,8,["modelValue"])]),_:1}),l(m,{label:"系统内置",prop:"configType"},{default:n(()=>[l(ne,{modelValue:t(u).configType,"onUpdate:modelValue":e[2]||(e[2]=o=>t(u).configType=o),placeholder:"系统内置",clearable:"",style:{width:"240px"}},{default:n(()=>[(d(!0),R(Q,null,j(t(N),o=>(d(),_(oe,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"创建时间",style:{width:"308px"}},{default:n(()=>[l(te,{modelValue:t(C),"onUpdate:modelValue":e[3]||(e[3]=o=>I(C)?C.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),l(m,null,{default:n(()=>[l(f,{type:"primary",icon:"Search",onClick:x},{default:n(()=>e[13]||(e[13]=[s("搜索")])),_:1,__:[13]}),l(f,{icon:"Refresh",onClick:O},{default:n(()=>e[14]||(e[14]=[s("重置")])),_:1,__:[14]})]),_:1})]),_:1},8,["model"]),[[E,t(h)]]),l(ie,{gutter:10,class:"mb8"},{default:n(()=>[l(k,{span:1.5},{default:n(()=>[c((d(),_(f,{type:"primary",plain:"",icon:"Plus",onClick:X},{default:n(()=>e[15]||(e[15]=[s("新增")])),_:1,__:[15]})),[[w,["system:config:add"]]])]),_:1}),l(k,{span:1.5},{default:n(()=>[c((d(),_(f,{type:"success",plain:"",icon:"Edit",disabled:t(q),onClick:Y},{default:n(()=>e[16]||(e[16]=[s("修改")])),_:1,__:[16]},8,["disabled"])),[[w,["system:config:edit"]]])]),_:1}),l(k,{span:1.5},{default:n(()=>[c((d(),_(f,{type:"danger",plain:"",icon:"Delete",disabled:t(P),onClick:F},{default:n(()=>e[17]||(e[17]=[s("删除")])),_:1,__:[17]},8,["disabled"])),[[w,["system:config:remove"]]])]),_:1}),l(k,{span:1.5},{default:n(()=>[c((d(),_(f,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:n(()=>e[18]||(e[18]=[s("导出")])),_:1,__:[18]})),[[w,["system:config:export"]]])]),_:1}),l(k,{span:1.5},{default:n(()=>[c((d(),_(f,{type:"danger",plain:"",icon:"Refresh",onClick:le},{default:n(()=>e[19]||(e[19]=[s("刷新缓存")])),_:1,__:[19]})),[[w,["system:config:remove"]]])]),_:1}),l(ae,{showSearch:t(h),"onUpdate:showSearch":e[4]||(e[4]=o=>I(h)?h.value=o:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),c((d(),_(re,{data:t($),onSelectionChange:W},{default:n(()=>[l(y,{type:"selection",width:"55",align:"center"}),l(y,{label:"参数主键",align:"center",prop:"configId"}),l(y,{label:"参数名称",align:"center",prop:"configName","show-overflow-tooltip":!0}),l(y,{label:"参数键名",align:"center",prop:"configKey","show-overflow-tooltip":!0}),l(y,{label:"参数键值",align:"center",prop:"configValue","show-overflow-tooltip":!0}),l(y,{label:"系统内置",align:"center",prop:"configType"},{default:n(o=>[l(ue,{options:t(N),value:o.row.configType},null,8,["options","value"])]),_:1}),l(y,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),l(y,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:n(o=>[A("span",null,M(r.parseTime(o.row.createTime)),1)]),_:1}),l(y,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:n(o=>[c((d(),_(f,{link:"",type:"primary",icon:"Edit",onClick:ge=>Y(o.row)},{default:n(()=>e[20]||(e[20]=[s("修改")])),_:2,__:[20]},1032,["onClick"])),[[w,["system:config:edit"]]]),c((d(),_(f,{link:"",type:"primary",icon:"Delete",onClick:ge=>F(o.row)},{default:n(()=>e[21]||(e[21]=[s("删除")])),_:2,__:[21]},1032,["onClick"])),[[w,["system:config:remove"]]])]),_:1})]),_:1},8,["data"])),[[me,t(S)]]),c(l(de,{total:t(U),page:t(u).pageNum,"onUpdate:page":e[5]||(e[5]=o=>t(u).pageNum=o),limit:t(u).pageSize,"onUpdate:limit":e[6]||(e[6]=o=>t(u).pageSize=o),onPagination:V},null,8,["total","page","limit"]),[[E,t(U)>0]]),l(fe,{title:t(K),modelValue:t(v),"onUpdate:modelValue":e[12]||(e[12]=o=>I(v)?v.value=o:null),width:"500px","append-to-body":""},{footer:n(()=>[A("div",Ne,[l(f,{type:"primary",onClick:Z},{default:n(()=>e[22]||(e[22]=[s("确 定")])),_:1,__:[22]}),l(f,{onClick:J},{default:n(()=>e[23]||(e[23]=[s("取 消")])),_:1,__:[23]})])]),default:n(()=>[l(z,{ref:"configRef",model:t(i),rules:t(H),"label-width":"80px"},{default:n(()=>[l(m,{label:"参数名称",prop:"configName"},{default:n(()=>[l(b,{modelValue:t(i).configName,"onUpdate:modelValue":e[7]||(e[7]=o=>t(i).configName=o),placeholder:"请输入参数名称"},null,8,["modelValue"])]),_:1}),l(m,{label:"参数键名",prop:"configKey"},{default:n(()=>[l(b,{modelValue:t(i).configKey,"onUpdate:modelValue":e[8]||(e[8]=o=>t(i).configKey=o),placeholder:"请输入参数键名"},null,8,["modelValue"])]),_:1}),l(m,{label:"参数键值",prop:"configValue"},{default:n(()=>[l(b,{modelValue:t(i).configValue,"onUpdate:modelValue":e[9]||(e[9]=o=>t(i).configValue=o),type:"textarea",placeholder:"请输入参数键值"},null,8,["modelValue"])]),_:1}),l(m,{label:"系统内置",prop:"configType"},{default:n(()=>[l(pe,{modelValue:t(i).configType,"onUpdate:modelValue":e[10]||(e[10]=o=>t(i).configType=o)},{default:n(()=>[(d(!0),R(Q,null,j(t(N),o=>(d(),_(se,{key:o.value,value:o.value},{default:n(()=>[s(M(o.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注",prop:"remark"},{default:n(()=>[l(b,{modelValue:t(i).remark,"onUpdate:modelValue":e[11]||(e[11]=o=>t(i).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ke as default};
