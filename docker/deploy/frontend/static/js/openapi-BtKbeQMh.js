import{S as t}from"./index-CdiCNU81.js";function s(e){return t({url:"/system/openapi/key/list",method:"get",params:e})}function p(e){return t({url:"/system/openapi/key/"+e,method:"get"})}function r(e){return t({url:"/system/openapi/key",method:"post",data:e})}function a(e){return t({url:"/system/openapi/key",method:"put",data:e})}function o(e){return t({url:"/system/openapi/key/"+e,method:"delete"})}function u(e){return t({url:"/system/openapi/key/changeStatus",method:"put",data:e})}function i(e){return t({url:"/system/openapi/key/regenerate/"+e,method:"put"})}function m(){return t({url:"/system/openapi/key/groups",method:"get"})}function y(e){return t({url:"/system/openapi/log/list",method:"get",params:e})}function l(e){return t({url:"/system/openapi/log/"+e,method:"delete"})}export{p as a,r as b,u as c,o as d,y as e,l as f,m as g,s as l,i as r,a as u};
