import{ac as V,ad as U,r as s,e as a,j as p,o as u,i as l,f as n,l as r,k as q,c as j,J as E,K as J,n as f,t as K}from"./index-CdiCNU81.js";const S={__name:"CodeTypeDialog",props:V({showFileName:Boolean},{modelValue:{},modelModifiers:{}}),emits:V(["confirm"],["update:modelValue"]),setup(m,{emit:b}){const d=U(m,"modelValue"),v=m,N=b,t=s({fileName:void 0,type:"file"}),_=s(),k={fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},C=s([{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]);function w(){v.showFileName&&(t.value.fileName=`${+new Date}.vue`)}function i(){d.value=!1}function x(){_.value.validate(c=>{c&&(N("confirm",{...t.value}),i())})}return(c,e)=>{const F=a("el-radio-button"),B=a("el-radio-group"),g=a("el-form-item"),D=a("el-input"),T=a("el-form"),y=a("el-button"),M=a("el-dialog");return u(),p(M,{modelValue:d.value,"onUpdate:modelValue":e[2]||(e[2]=o=>d.value=o),width:"500px",title:"选择生成类型",onOpen:w,onClose:i},{footer:l(()=>[n(y,{onClick:i},{default:l(()=>e[3]||(e[3]=[f("取消")])),_:1,__:[3]}),n(y,{type:"primary",onClick:x},{default:l(()=>e[4]||(e[4]=[f("确定")])),_:1,__:[4]})]),default:l(()=>[n(T,{ref_key:"codeTypeForm",ref:_,model:r(t),rules:k,"label-width":"100px"},{default:l(()=>[n(g,{label:"生成类型",prop:"type"},{default:l(()=>[n(B,{modelValue:r(t).type,"onUpdate:modelValue":e[0]||(e[0]=o=>r(t).type=o)},{default:l(()=>[(u(!0),j(E,null,J(r(C),(o,O)=>(u(),p(F,{key:O,label:o.value},{default:l(()=>[f(K(o.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),m.showFileName?(u(),p(g,{key:0,label:"文件名",prop:"fileName"},{default:l(()=>[n(D,{modelValue:r(t).fileName,"onUpdate:modelValue":e[1]||(e[1]=o=>r(t).fileName=o),placeholder:"请输入文件名",clearable:""},null,8,["modelValue"])]),_:1})):q("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])}}};export{S as default};
