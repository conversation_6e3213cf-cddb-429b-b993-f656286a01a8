import{g as H}from"./project-Dl87E-1X.js";import{_ as Q,z as W,d as X,u as Y,a as Z,r as g,B as ee,e as c,c as i,o as u,f as l,l as r,i as t,C as te,n as a,t as o,k as f,J as y,K as b,j as k,h as n}from"./index-CdiCNU81.js";const se={class:"app-container"},le={key:0},ne={class:"activity-container"},oe={class:"activity-header"},ae={class:"activity-title"},re={class:"activity-content"},ue={key:0},pe={key:1},ie={class:"activity-header"},de={class:"activity-title"},me={class:"activity-content"},ce={key:0},_e={key:1},fe={key:2},ge={class:"activity-header"},ye={class:"activity-title"},be={class:"activity-content"},ke={key:0},ve=W({name:"ProjectDetail"}),je=Object.assign(ve,{setup(Te){const{proxy:T}=X(),z=Y(),h=Z(),{sdl_project_scope:I,sdl_project_status:R,sdl_project_type:A}=T.useDict("sdl_project_scope","sdl_project_status","sdl_project_type"),p=g({}),x=g("basic"),V=g("项目详情"),E=g([{timestamp:"2025-01-15 10:00",title:"需求初步评审",status:"已完成",type:"success",reviewer:"张三, 李四",content:"对项目需求文档进行初步评审，确认功能需求的合理性和完整性",result:"评审通过，建议优化部分功能设计",attachments:["需求文档v1.0.pdf","评审记录.docx"]},{timestamp:"2025-01-20 14:30",title:"需求详细评审",status:"已完成",type:"success",reviewer:"王五, 赵六",content:"详细评审修改后的需求文档，确认所有功能点和非功能需求",result:"评审通过，需求冻结",attachments:["需求文档v2.0.pdf"]},{timestamp:"2025-02-01 09:00",title:"变更需求评审",status:"进行中",type:"primary",reviewer:"张三",content:"评审客户提出的新增需求变更",result:"",attachments:[]}]),P=g([{timestamp:"2025-01-25 16:00",title:"静态代码安全扫描",status:"已完成",type:"success",tester:"安全测试团队",testType:"SAST",content:"使用静态分析工具对代码进行安全漏洞扫描",vulnerabilities:"发现3个中危漏洞，8个低危漏洞",riskLevel:"中等",reports:["SAST扫描报告.pdf","漏洞修复建议.docx"]},{timestamp:"2025-02-03 10:30",title:"渗透测试",status:"已完成",type:"success",tester:"外部安全顾问",testType:"黑盒测试",content:"模拟黑客攻击，测试系统的安全防护能力",vulnerabilities:"发现1个高危漏洞，2个中危漏洞",riskLevel:"高",reports:["渗透测试报告.pdf"]},{timestamp:"2025-02-10 14:00",title:"安全基线检查",status:"计划中",type:"info",tester:"运维安全团队",testType:"配置检查",content:"检查系统配置是否符合安全基线要求",vulnerabilities:"",riskLevel:"",reports:[]}]),$=g([{timestamp:"2025-01-18 11:00",title:"项目启动会议",status:"已完成",type:"success",owner:"项目经理",content:"召开项目启动会议，明确项目目标、范围、时间计划和团队职责",result:"会议成功召开，所有成员明确各自职责"},{timestamp:"2025-01-30 15:30",title:"技术方案评审",status:"已完成",type:"success",owner:"技术架构师",content:"评审技术架构设计方案，确认技术选型和架构合理性",result:"技术方案通过评审，获得批准实施"},{timestamp:"2025-02-05 09:30",title:"代码评审",status:"进行中",type:"primary",owner:"开发团队Leader",content:"对核心模块代码进行同行评审，确保代码质量",result:""}]);function q(m){return m?m.split(",").map(s=>s.trim()).filter(s=>s):[]}function w(m){return{已完成:"success",进行中:"primary",计划中:"info",已取消:"danger",暂停:"warning"}[m]||"info"}function F(m){return{高:"danger",中等:"warning",低:"success"}[m]||"info"}function J(){h.back()}function K(){const m=z.query.id;m?H(m).then(s=>{p.value=s.data,V.value=`${p.value.projectName}`}).catch(()=>{T.$modal.msgError("获取项目详情失败"),h.back()}):(T.$modal.msgError("缺少项目ID参数"),h.back())}return ee(()=>{K()}),(m,s)=>{const O=c("el-page-header"),d=c("el-descriptions-item"),L=c("dict-tag"),v=c("el-tag"),U=c("el-descriptions"),D=c("el-tab-pane"),N=c("el-link"),_=c("el-card"),B=c("el-timeline-item"),C=c("el-timeline"),G=c("el-tabs");return u(),i("div",se,[l(O,{onBack:J,content:r(V)},null,8,["content"]),l(_,{class:"box-card",style:{"margin-top":"20px"}},{default:t(()=>[l(G,{modelValue:r(x),"onUpdate:modelValue":s[0]||(s[0]=e=>te(x)?x.value=e:null)},{default:t(()=>[l(D,{label:"项目信息",name:"basic"},{default:t(()=>[l(U,{column:2,border:""},{default:t(()=>[l(d,{label:"项目编号"},{default:t(()=>[a(o(r(p).projectCode||"-"),1)]),_:1}),l(d,{label:"项目名称"},{default:t(()=>[a(o(r(p).projectName||"-"),1)]),_:1}),l(d,{label:"项目状态"},{default:t(()=>[l(L,{options:r(R),value:r(p).projectStatus},null,8,["options","value"])]),_:1}),l(d,{label:"项目类型"},{default:t(()=>[l(L,{options:r(A),value:r(p).projectType},null,8,["options","value"])]),_:1}),l(d,{label:"项目经理"},{default:t(()=>[a(o(r(p).projectManager||"-"),1)]),_:1}),l(d,{label:"开始时间"},{default:t(()=>[a(o(r(p).startTime||"-"),1)]),_:1}),l(d,{label:"立项范围"},{default:t(()=>[l(L,{options:r(I),value:r(p).projectScope},null,8,["options","value"])]),_:1}),l(d,{label:"归属部门"},{default:t(()=>[a(o(r(p).belongDepartment||"-"),1)]),_:1}),l(d,{label:"项目标签"},{default:t(()=>[(u(!0),i(y,null,b(q(r(p).projectLabel),e=>(u(),k(v,{key:e,style:{"margin-right":"8px"}},{default:t(()=>[a(o(e),1)]),_:2},1024))),128)),r(p).projectLabel?f("",!0):(u(),i("span",le,"-"))]),_:1}),l(d,{label:"项目成员"},{default:t(()=>[a(o(r(p).projectMembers||"-"),1)]),_:1}),l(d,{label:"创建时间"},{default:t(()=>[a(o(m.parseTime(r(p).createTime)),1)]),_:1}),l(d,{label:"更新时间"},{default:t(()=>[a(o(m.parseTime(r(p).updateTime)),1)]),_:1}),l(d,{label:"备注",span:2},{default:t(()=>[a(o(r(p).remark||"-"),1)]),_:1})]),_:1})]),_:1}),l(D,{label:"活动记录",name:"activities"},{default:t(()=>[n("div",ne,[l(_,{class:"activity-card",header:"需求评审记录"},{default:t(()=>[l(C,null,{default:t(()=>[(u(!0),i(y,null,b(r(E),(e,j)=>(u(),k(B,{key:j,timestamp:e.timestamp,type:e.type},{default:t(()=>[l(_,{shadow:"hover",style:{"margin-bottom":"12px"}},{default:t(()=>[n("div",oe,[n("span",ae,o(e.title),1),l(v,{type:w(e.status),size:"small"},{default:t(()=>[a(o(e.status),1)]),_:2},1032,["type"])]),n("div",re,[n("p",null,[s[1]||(s[1]=n("strong",null,"评审人员：",-1)),a(o(e.reviewer),1)]),n("p",null,[s[2]||(s[2]=n("strong",null,"评审内容：",-1)),a(o(e.content),1)]),e.result?(u(),i("p",ue,[s[3]||(s[3]=n("strong",null,"评审结果：",-1)),a(o(e.result),1)])):f("",!0),e.attachments&&e.attachments.length>0?(u(),i("div",pe,[s[4]||(s[4]=n("strong",null,"相关附件：",-1)),(u(!0),i(y,null,b(e.attachments,(M,S)=>(u(),k(N,{key:S,type:"primary",style:{"margin-left":"8px"}},{default:t(()=>[a(o(M),1)]),_:2},1024))),128))])):f("",!0)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1}),l(_,{class:"activity-card",header:"安全测试记录",style:{"margin-top":"20px"}},{default:t(()=>[l(C,null,{default:t(()=>[(u(!0),i(y,null,b(r(P),(e,j)=>(u(),k(B,{key:j,timestamp:e.timestamp,type:e.type},{default:t(()=>[l(_,{shadow:"hover",style:{"margin-bottom":"12px"}},{default:t(()=>[n("div",ie,[n("span",de,o(e.title),1),l(v,{type:w(e.status),size:"small"},{default:t(()=>[a(o(e.status),1)]),_:2},1032,["type"])]),n("div",me,[n("p",null,[s[5]||(s[5]=n("strong",null,"测试人员：",-1)),a(o(e.tester),1)]),n("p",null,[s[6]||(s[6]=n("strong",null,"测试类型：",-1)),a(o(e.testType),1)]),n("p",null,[s[7]||(s[7]=n("strong",null,"测试内容：",-1)),a(o(e.content),1)]),e.vulnerabilities?(u(),i("p",ce,[s[8]||(s[8]=n("strong",null,"发现漏洞：",-1)),a(o(e.vulnerabilities),1)])):f("",!0),e.riskLevel?(u(),i("p",_e,[s[9]||(s[9]=n("strong",null,"风险等级：",-1)),l(v,{type:F(e.riskLevel),size:"small"},{default:t(()=>[a(o(e.riskLevel),1)]),_:2},1032,["type"])])):f("",!0),e.reports&&e.reports.length>0?(u(),i("div",fe,[s[10]||(s[10]=n("strong",null,"测试报告：",-1)),(u(!0),i(y,null,b(e.reports,(M,S)=>(u(),k(N,{key:S,type:"primary",style:{"margin-left":"8px"}},{default:t(()=>[a(o(M),1)]),_:2},1024))),128))])):f("",!0)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1}),l(_,{class:"activity-card",header:"其他活动记录",style:{"margin-top":"20px"}},{default:t(()=>[l(C,null,{default:t(()=>[(u(!0),i(y,null,b(r($),(e,j)=>(u(),k(B,{key:j,timestamp:e.timestamp,type:e.type},{default:t(()=>[l(_,{shadow:"hover",style:{"margin-bottom":"12px"}},{default:t(()=>[n("div",ge,[n("span",ye,o(e.title),1),l(v,{type:w(e.status),size:"small"},{default:t(()=>[a(o(e.status),1)]),_:2},1032,["type"])]),n("div",be,[n("p",null,[s[11]||(s[11]=n("strong",null,"负责人：",-1)),a(o(e.owner),1)]),n("p",null,[s[12]||(s[12]=n("strong",null,"活动内容：",-1)),a(o(e.content),1)]),e.result?(u(),i("p",ke,[s[13]||(s[13]=n("strong",null,"结果：",-1)),a(o(e.result),1)])):f("",!0)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}}),we=Q(je,[["__scopeId","data-v-d4553fce"]]);export{we as default};
