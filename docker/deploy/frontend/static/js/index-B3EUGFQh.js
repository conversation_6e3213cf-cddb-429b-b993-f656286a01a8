import{a3 as nl,r as b,x as J,w as ve,B as Ue,a4 as Ae,j as x,a5 as Z,o as h,a6 as ol,a7 as oe,d as qe,c as ee,a8 as sl,a1 as il,l as i,a9 as ul,D as Me,z as rl,a as dl,aa as pl,A as De,ab as ml,I as vl,e as g,F as Ee,f as n,i as s,h as A,C as ze,G as E,m as Te,J as pe,K as me,n as M,H as Oe,k as $,t as Le}from"./index-CdiCNU81.js";import{e as cl,l as fl,f as Be,h as hl,i as _l,r as gl,j as xl,k as bl}from"./user-CV6IIF7D.js";const yl={__name:"splitpanes",props:{horizontal:{type:Boolean,default:!1},pushOtherPanes:{type:Boolean,default:!0},maximizePanes:{type:Boolean,default:!0},rtl:{type:Boolean,default:!1},firstSplitter:{type:Boolean,default:!1}},emits:["ready","resize","resized","pane-click","pane-maximize","pane-add","pane-remove","splitter-click","splitter-dblclick"],setup(ce,{emit:le}){const T=le,v=ce,X=nl(),m=b([]),q=J(()=>m.value.reduce((e,t)=>(e[~~t.id]=t)&&e,{})),w=J(()=>m.value.length),V=b(null),D=b(!1),y=b({mouseDown:!1,dragging:!1,activeSplitter:null,cursorOffset:0}),U=b({splitter:null,timeoutId:null}),Q=J(()=>({[`splitpanes splitpanes--${v.horizontal?"horizontal":"vertical"}`]:!0,"splitpanes--dragging":y.value.dragging})),K=()=>{document.addEventListener("mousemove",_,{passive:!1}),document.addEventListener("mouseup",L),"ontouchstart"in window&&(document.addEventListener("touchmove",_,{passive:!1}),document.addEventListener("touchend",L))},j=()=>{document.removeEventListener("mousemove",_,{passive:!1}),document.removeEventListener("mouseup",L),"ontouchstart"in window&&(document.removeEventListener("touchmove",_,{passive:!1}),document.removeEventListener("touchend",L))},O=(e,t)=>{const r=e.target.closest(".splitpanes__splitter");if(r){const{left:a,top:u}=r.getBoundingClientRect(),{clientX:p,clientY:f}="ontouchstart"in window&&e.touches?e.touches[0]:e;y.value.cursorOffset=v.horizontal?f-u:p-a}K(),y.value.mouseDown=!0,y.value.activeSplitter=t},_=e=>{y.value.mouseDown&&(e.preventDefault(),y.value.dragging=!0,requestAnimationFrame(()=>{B(ue(e)),R("resize",{event:e},!0)}))},L=e=>{y.value.dragging&&(window.getSelection().removeAllRanges(),R("resized",{event:e},!0)),y.value.mouseDown=!1,y.value.activeSplitter=null,setTimeout(()=>{y.value.dragging=!1,j()},100)},te=(e,t)=>{"ontouchstart"in window&&(e.preventDefault(),U.value.splitter===t?(clearTimeout(U.value.timeoutId),U.value.timeoutId=null,se(e,t),U.value.splitter=null):(U.value.splitter=t,U.value.timeoutId=setTimeout(()=>U.value.splitter=null,500))),y.value.dragging||R("splitter-click",{event:e,index:t},!0)},se=(e,t)=>{if(R("splitter-dblclick",{event:e,index:t},!0),v.maximizePanes){let r=0;m.value=m.value.map((a,u)=>(a.size=u===t?a.max:a.min,u!==t&&(r+=a.min),a)),m.value[t].size-=r,R("pane-maximize",{event:e,index:t,pane:m.value[t]}),R("resized",{event:e,index:t},!0)}},ie=(e,t)=>{R("pane-click",{event:e,index:q.value[t].index,pane:q.value[t]})},ue=e=>{const t=V.value.getBoundingClientRect(),{clientX:r,clientY:a}="ontouchstart"in window&&e.touches?e.touches[0]:e;return{x:r-(v.horizontal?0:y.value.cursorOffset)-t.left,y:a-(v.horizontal?y.value.cursorOffset:0)-t.top}},P=e=>{e=e[v.horizontal?"y":"x"];const t=V.value[v.horizontal?"clientHeight":"clientWidth"];return v.rtl&&!v.horizontal&&(e=t-e),e*100/t},B=e=>{const t=y.value.activeSplitter;let r={prevPanesSize:k(t),nextPanesSize:c(t),prevReachedMinPanes:0,nextReachedMinPanes:0};const a=0+(v.pushOtherPanes?0:r.prevPanesSize),u=100-(v.pushOtherPanes?0:r.nextPanesSize),p=Math.max(Math.min(P(e),u),a);let f=[t,t+1],N=m.value[f[0]]||null,d=m.value[f[1]]||null;const l=N.max<100&&p>=N.max+r.prevPanesSize,z=d.max<100&&p<=100-(d.max+c(t+1));if(l||z){l?(N.size=N.max,d.size=Math.max(100-N.max-r.prevPanesSize-r.nextPanesSize,0)):(N.size=Math.max(100-d.max-r.prevPanesSize-c(t+1),0),d.size=d.max);return}if(v.pushOtherPanes){const ge=ke(r,p);if(!ge)return;({sums:r,panesToResize:f}=ge),N=m.value[f[0]]||null,d=m.value[f[1]]||null}N!==null&&(N.size=Math.min(Math.max(p-r.prevPanesSize-r.prevReachedMinPanes,N.min),N.max)),d!==null&&(d.size=Math.min(Math.max(100-p-r.nextPanesSize-r.nextReachedMinPanes,d.min),d.max))},ke=(e,t)=>{const r=y.value.activeSplitter,a=[r,r+1];return t<e.prevPanesSize+m.value[a[0]].min&&(a[0]=we(r).index,e.prevReachedMinPanes=0,a[0]<r&&m.value.forEach((u,p)=>{p>a[0]&&p<=r&&(u.size=u.min,e.prevReachedMinPanes+=u.min)}),e.prevPanesSize=k(a[0]),a[0]===void 0)?(e.prevReachedMinPanes=0,m.value[0].size=m.value[0].min,m.value.forEach((u,p)=>{p>0&&p<=r&&(u.size=u.min,e.prevReachedMinPanes+=u.min)}),m.value[a[1]].size=100-e.prevReachedMinPanes-m.value[0].min-e.prevPanesSize-e.nextPanesSize,null):t>100-e.nextPanesSize-m.value[a[1]].min&&(a[1]=Se(r).index,e.nextReachedMinPanes=0,a[1]>r+1&&m.value.forEach((u,p)=>{p>r&&p<a[1]&&(u.size=u.min,e.nextReachedMinPanes+=u.min)}),e.nextPanesSize=c(a[1]-1),a[1]===void 0)?(e.nextReachedMinPanes=0,m.value.forEach((u,p)=>{p<w.value-1&&p>=r+1&&(u.size=u.min,e.nextReachedMinPanes+=u.min)}),m.value[a[0]].size=100-e.prevPanesSize-c(a[0]-1),null):{sums:e,panesToResize:a}},k=e=>m.value.reduce((t,r,a)=>t+(a<e?r.size:0),0),c=e=>m.value.reduce((t,r,a)=>t+(a>e+1?r.size:0),0),we=e=>[...m.value].reverse().find(t=>t.index<e&&t.size>t.min)||{},Se=e=>m.value.find(t=>t.index>e+1&&t.size>t.min)||{},F=()=>{var e;const t=Array.from(((e=V.value)==null?void 0:e.children)||[]);for(const r of t){const a=r.classList.contains("splitpanes__pane"),u=r.classList.contains("splitpanes__splitter");!a&&!u&&(r.remove(),console.warn("Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed."))}},fe=(e,t,r=!1)=>{const a=e-1,u=document.createElement("div");u.classList.add("splitpanes__splitter"),r||(u.onmousedown=p=>O(p,a),typeof window<"u"&&"ontouchstart"in window&&(u.ontouchstart=p=>O(p,a)),u.onclick=p=>te(p,a+1)),u.ondblclick=p=>se(p,a+1),t.parentNode.insertBefore(u,t)},he=e=>{e.onmousedown=void 0,e.onclick=void 0,e.ondblclick=void 0,e.remove()},ae=()=>{var e;const t=Array.from(((e=V.value)==null?void 0:e.children)||[]);for(const a of t)a.className.includes("splitpanes__splitter")&&he(a);let r=0;for(const a of t)a.className.includes("splitpanes__pane")&&(!r&&v.firstSplitter?fe(r,a,!0):r&&fe(r,a),r++)},G=({uid:e,...t})=>{const r=q.value[e];for(const[a,u]of Object.entries(t))r[a]=u},Ve=e=>{var t;let r=-1;Array.from(((t=V.value)==null?void 0:t.children)||[]).some(a=>(a.className.includes("splitpanes__pane")&&r++,a.isSameNode(e.el))),m.value.splice(r,0,{...e,index:r}),m.value.forEach((a,u)=>a.index=u),D.value&&Me(()=>{ae(),re({addedPane:m.value[r]}),R("pane-add",{pane:m.value[r]})})},_e=e=>{const t=m.value.findIndex(a=>a.id===e);m.value[t].el=null;const r=m.value.splice(t,1)[0];m.value.forEach((a,u)=>a.index=u),Me(()=>{ae(),R("pane-remove",{pane:r}),re({removedPane:{...r}})})},re=(e={})=>{!e.addedPane&&!e.removedPane?Ne():m.value.some(t=>t.givenSize!==null||t.min||t.max<100)?Ie(e):Pe(),D.value&&R("resized")},Pe=()=>{const e=100/w.value;let t=0;const r=[],a=[];for(const u of m.value)u.size=Math.max(Math.min(e,u.max),u.min),t-=u.size,u.size>=u.max&&r.push(u.id),u.size<=u.min&&a.push(u.id);t>.1&&de(t,r,a)},Ne=()=>{let e=100;const t=[],r=[];let a=0;for(const p of m.value)e-=p.size,p.givenSize!==null&&a++,p.size>=p.max&&t.push(p.id),p.size<=p.min&&r.push(p.id);let u=100;if(e>.1){for(const p of m.value)p.givenSize===null&&(p.size=Math.max(Math.min(e/(w.value-a),p.max),p.min)),u-=p.size;u>.1&&de(u,t,r)}},Ie=({addedPane:e,removedPane:t}={})=>{let r=100/w.value,a=0;const u=[],p=[];((e==null?void 0:e.givenSize)??null)!==null&&(r=(100-e.givenSize)/(w.value-1));for(const f of m.value)a-=f.size,f.size>=f.max&&u.push(f.id),f.size<=f.min&&p.push(f.id);if(!(Math.abs(a)<.1)){for(const f of m.value)(e==null?void 0:e.givenSize)!==null&&(e==null?void 0:e.id)===f.id||(f.size=Math.max(Math.min(r,f.max),f.min)),a-=f.size,f.size>=f.max&&u.push(f.id),f.size<=f.min&&p.push(f.id);a>.1&&de(a,u,p)}},de=(e,t,r)=>{let a;e>0?a=e/(w.value-t.length):a=e/(w.value-r.length),m.value.forEach((u,p)=>{if(e>0&&!t.includes(u.id)){const f=Math.max(Math.min(u.size+a,u.max),u.min),N=f-u.size;e-=N,u.size=f}else if(!r.includes(u.id)){const f=Math.max(Math.min(u.size+a,u.max),u.min),N=f-u.size;e-=N,u.size=f}}),Math.abs(e)>.1&&Me(()=>{D.value&&console.warn("Splitpanes: Could not resize panes correctly due to their constraints.")})},R=(e,t=void 0,r=!1)=>{const a=(t==null?void 0:t.index)??y.value.activeSplitter??null;T(e,{...t,...a!==null&&{index:a},...r&&a!==null&&{prevPane:m.value[a-(v.firstSplitter?1:0)],nextPane:m.value[a+(v.firstSplitter?0:1)]},panes:m.value.map(u=>({min:u.min,max:u.max,size:u.size}))})};ve(()=>v.firstSplitter,()=>ae()),Ue(()=>{F(),ae(),re(),R("ready"),D.value=!0}),Ae(()=>D.value=!1);const Ce=()=>{var e;return ul("div",{ref:V,class:Q.value},(e=X.default)==null?void 0:e.call(X))};return Z("panes",m),Z("indexedPanes",q),Z("horizontal",J(()=>v.horizontal)),Z("requestUpdate",G),Z("onPaneAdd",Ve),Z("onPaneRemove",_e),Z("onPaneClick",ie),(e,t)=>(h(),x(ol(Ce)))}},Fe={__name:"pane",props:{size:{type:[Number,String]},minSize:{type:[Number,String],default:0},maxSize:{type:[Number,String],default:100}},setup(ce){var le;const T=ce,v=oe("requestUpdate"),X=oe("onPaneAdd"),m=oe("horizontal"),q=oe("onPaneRemove"),w=oe("onPaneClick"),V=(le=qe())==null?void 0:le.uid,D=oe("indexedPanes"),y=J(()=>D.value[V]),U=b(null),Q=J(()=>{const _=isNaN(T.size)||T.size===void 0?0:parseFloat(T.size);return Math.max(Math.min(_,j.value),K.value)}),K=J(()=>{const _=parseFloat(T.minSize);return isNaN(_)?0:_}),j=J(()=>{const _=parseFloat(T.maxSize);return isNaN(_)?100:_}),O=J(()=>{var _;return`${m.value?"height":"width"}: ${(_=y.value)==null?void 0:_.size}%`});return ve(()=>Q.value,_=>v({uid:V,size:_})),ve(()=>K.value,_=>v({uid:V,min:_})),ve(()=>j.value,_=>v({uid:V,max:_})),Ue(()=>{X({id:V,el:U.value,min:K.value,max:j.value,givenSize:T.size===void 0?null:Q.value,size:Q.value})}),Ae(()=>q(V)),(_,L)=>(h(),ee("div",{ref_key:"paneEl",ref:U,class:"splitpanes__pane",onClick:L[0]||(L[0]=te=>i(w)(te,_._.uid)),style:il(O.value)},[sl(_.$slots,"default")],4))}},zl={class:"app-container"},kl={class:"head-container"},wl={class:"head-container"},Sl={class:"dialog-footer"},Vl={class:"el-upload__tip text-center"},Pl={class:"el-upload__tip"},Nl={class:"dialog-footer"},Il=rl({name:"User"}),Ul=Object.assign(Il,{setup(ce){const le=dl(),T=pl(),{proxy:v}=qe(),{sys_normal_disable:X,sys_user_sex:m}=v.useDict("sys_normal_disable","sys_user_sex"),q=b([]),w=b(!1),V=b(!0),D=b(!0),y=b([]),U=b(!0),Q=b(!0),K=b(0),j=b(""),O=b([]),_=b(""),L=b(void 0),te=b(void 0),se=b(void 0),ie=b([]),ue=b([]),P=De({open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+ml()},url:"/prod-api/system/user/importData"}),B=b([{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}]),ke=De({form:{},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:k,form:c,rules:we}=vl(ke),Se=(d,l)=>d?l.label.indexOf(d)!==-1:!0;ve(_,d=>{v.$refs.deptTreeRef.filter(d)});function F(){V.value=!0,fl(v.addDateRange(k.value,O.value)).then(d=>{V.value=!1,q.value=d.rows,K.value=d.total})}function fe(){cl().then(d=>{L.value=d.data,te.value=he(JSON.parse(JSON.stringify(d.data)))})}function he(d){return d.filter(l=>l.disabled?!1:(l.children&&l.children.length&&(l.children=he(l.children)),!0))}function ae(d){k.value.deptId=d.id,G()}function G(){k.value.pageNum=1,F()}function Ve(){O.value=[],v.resetForm("queryRef"),k.value.deptId=void 0,v.$refs.deptTreeRef.setCurrentKey(null),G()}function _e(d){const l=d.userId||y.value;v.$modal.confirm('是否确认删除用户编号为"'+l+'"的数据项？').then(function(){return hl(l)}).then(()=>{F(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function re(){v.download("system/user/export",{...k.value},`user_${new Date().getTime()}.xlsx`)}function Pe(d){let l=d.status==="0"?"启用":"停用";v.$modal.confirm('确认要"'+l+'""'+d.userName+'"用户吗?').then(function(){return _l(d.userId,d.status)}).then(()=>{v.$modal.msgSuccess(l+"成功")}).catch(function(){d.status=d.status==="0"?"1":"0"})}function Ne(d){const l=d.userId;le.push("/system/user-auth/role/"+l)}function Ie(d){v.$prompt('请输入"'+d.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:l=>{if(/<|>|"|'|\||\\/.test(l))return`不能包含非法字符：< > " ' \\ |`}}).then(({value:l})=>{gl(d.userId,l).then(z=>{v.$modal.msgSuccess("修改成功，新密码是："+l)})}).catch(()=>{})}function de(d){y.value=d.map(l=>l.userId),U.value=d.length!=1,Q.value=!d.length}function R(){P.title="用户导入",P.open=!0}function Ce(){v.download("system/user/importTemplate",{},`user_template_${new Date().getTime()}.xlsx`)}const e=(d,l,z)=>{P.isUploading=!0},t=(d,l,z)=>{P.open=!1,P.isUploading=!1,v.$refs.uploadRef.handleRemove(l),v.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+d.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),F()};function r(){v.$refs.uploadRef.submit()}function a(){c.value={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[]},v.resetForm("userRef")}function u(){w.value=!1,a()}function p(){a(),Be().then(d=>{ie.value=d.posts,ue.value=d.roles,w.value=!0,j.value="添加用户",c.value.password=se.value})}function f(d){a();const l=d.userId||y.value;Be(l).then(z=>{c.value=z.data,ie.value=z.posts,ue.value=z.roles,c.value.postIds=z.postIds,c.value.roleIds=z.roleIds,w.value=!0,j.value="修改用户",c.password=""})}function N(){v.$refs.userRef.validate(d=>{d&&(c.value.userId!=null?xl(c.value).then(l=>{v.$modal.msgSuccess("修改成功"),w.value=!1,F()}):bl(c.value).then(l=>{v.$modal.msgSuccess("新增成功"),w.value=!1,F()}))})}return Ue(()=>{fe(),F(),v.getConfigKey("sys.user.initPassword").then(d=>{se.value=d.msg})}),(d,l)=>{const z=g("el-input"),ge=g("el-tree"),S=g("el-col"),I=g("el-form-item"),xe=g("el-option"),be=g("el-select"),Ke=g("el-date-picker"),C=g("el-button"),Re=g("el-form"),je=g("right-toolbar"),W=g("el-row"),Y=g("el-table-column"),Ye=g("el-switch"),ye=g("el-tooltip"),He=g("el-table"),Je=g("pagination"),Qe=g("el-tree-select"),We=g("el-radio"),Xe=g("el-radio-group"),$e=g("el-dialog"),Ge=g("upload-filled"),Ze=g("el-icon"),el=g("el-checkbox"),ll=g("el-link"),tl=g("el-upload"),H=Ee("hasPermi"),al=Ee("loading");return h(),ee("div",zl,[n(W,{gutter:20},{default:s(()=>[n(i(yl),{horizontal:i(T).device==="mobile",class:"default-theme"},{default:s(()=>[n(i(Fe),{size:"16"},{default:s(()=>[n(S,null,{default:s(()=>[A("div",kl,[n(z,{modelValue:i(_),"onUpdate:modelValue":l[0]||(l[0]=o=>ze(_)?_.value=o:null),placeholder:"请输入部门名称",clearable:"","prefix-icon":"Search",style:{"margin-bottom":"20px"}},null,8,["modelValue"])]),A("div",wl,[n(ge,{data:i(L),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":Se,ref:"deptTreeRef","node-key":"id","highlight-current":"","default-expand-all":"",onNodeClick:ae},null,8,["data"])])]),_:1})]),_:1}),n(i(Fe),{size:"84"},{default:s(()=>[n(S,null,{default:s(()=>[E(n(Re,{model:i(k),ref:"queryRef",inline:!0,"label-width":"68px"},{default:s(()=>[n(I,{label:"用户名称",prop:"userName"},{default:s(()=>[n(z,{modelValue:i(k).userName,"onUpdate:modelValue":l[1]||(l[1]=o=>i(k).userName=o),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:Te(G,["enter"])},null,8,["modelValue"])]),_:1}),n(I,{label:"手机号码",prop:"phonenumber"},{default:s(()=>[n(z,{modelValue:i(k).phonenumber,"onUpdate:modelValue":l[2]||(l[2]=o=>i(k).phonenumber=o),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:Te(G,["enter"])},null,8,["modelValue"])]),_:1}),n(I,{label:"状态",prop:"status"},{default:s(()=>[n(be,{modelValue:i(k).status,"onUpdate:modelValue":l[3]||(l[3]=o=>i(k).status=o),placeholder:"用户状态",clearable:"",style:{width:"240px"}},{default:s(()=>[(h(!0),ee(pe,null,me(i(X),o=>(h(),x(xe,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(I,{label:"创建时间",style:{width:"308px"}},{default:s(()=>[n(Ke,{modelValue:i(O),"onUpdate:modelValue":l[4]||(l[4]=o=>ze(O)?O.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),n(I,null,{default:s(()=>[n(C,{type:"primary",icon:"Search",onClick:G},{default:s(()=>l[23]||(l[23]=[M("搜索")])),_:1,__:[23]}),n(C,{icon:"Refresh",onClick:Ve},{default:s(()=>l[24]||(l[24]=[M("重置")])),_:1,__:[24]})]),_:1})]),_:1},8,["model"]),[[Oe,i(D)]]),n(W,{gutter:10,class:"mb8"},{default:s(()=>[n(S,{span:1.5},{default:s(()=>[E((h(),x(C,{type:"primary",plain:"",icon:"Plus",onClick:p},{default:s(()=>l[25]||(l[25]=[M("新增")])),_:1,__:[25]})),[[H,["system:user:add"]]])]),_:1}),n(S,{span:1.5},{default:s(()=>[E((h(),x(C,{type:"success",plain:"",icon:"Edit",disabled:i(U),onClick:f},{default:s(()=>l[26]||(l[26]=[M("修改")])),_:1,__:[26]},8,["disabled"])),[[H,["system:user:edit"]]])]),_:1}),n(S,{span:1.5},{default:s(()=>[E((h(),x(C,{type:"danger",plain:"",icon:"Delete",disabled:i(Q),onClick:_e},{default:s(()=>l[27]||(l[27]=[M("删除")])),_:1,__:[27]},8,["disabled"])),[[H,["system:user:remove"]]])]),_:1}),n(S,{span:1.5},{default:s(()=>[E((h(),x(C,{type:"info",plain:"",icon:"Upload",onClick:R},{default:s(()=>l[28]||(l[28]=[M("导入")])),_:1,__:[28]})),[[H,["system:user:import"]]])]),_:1}),n(S,{span:1.5},{default:s(()=>[E((h(),x(C,{type:"warning",plain:"",icon:"Download",onClick:re},{default:s(()=>l[29]||(l[29]=[M("导出")])),_:1,__:[29]})),[[H,["system:user:export"]]])]),_:1}),n(je,{showSearch:i(D),"onUpdate:showSearch":l[5]||(l[5]=o=>ze(D)?D.value=o:null),onQueryTable:F,columns:i(B)},null,8,["showSearch","columns"])]),_:1}),E((h(),x(He,{data:i(q),onSelectionChange:de},{default:s(()=>[n(Y,{type:"selection",width:"50",align:"center"}),i(B)[0].visible?(h(),x(Y,{label:"用户编号",align:"center",key:"userId",prop:"userId"})):$("",!0),i(B)[1].visible?(h(),x(Y,{label:"用户名称",align:"center",key:"userName",prop:"userName","show-overflow-tooltip":!0})):$("",!0),i(B)[2].visible?(h(),x(Y,{label:"用户昵称",align:"center",key:"nickName",prop:"nickName","show-overflow-tooltip":!0})):$("",!0),i(B)[3].visible?(h(),x(Y,{label:"部门",align:"center",key:"deptName",prop:"dept.deptName","show-overflow-tooltip":!0})):$("",!0),i(B)[4].visible?(h(),x(Y,{label:"手机号码",align:"center",key:"phonenumber",prop:"phonenumber",width:"120"})):$("",!0),i(B)[5].visible?(h(),x(Y,{label:"状态",align:"center",key:"status"},{default:s(o=>[n(Ye,{modelValue:o.row.status,"onUpdate:modelValue":ne=>o.row.status=ne,"active-value":"0","inactive-value":"1",onChange:ne=>Pe(o.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):$("",!0),i(B)[6].visible?(h(),x(Y,{key:6,label:"创建时间",align:"center",prop:"createTime",width:"160"},{default:s(o=>[A("span",null,Le(d.parseTime(o.row.createTime)),1)]),_:1})):$("",!0),n(Y,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:s(o=>[o.row.userId!==1?(h(),x(ye,{key:0,content:"修改",placement:"top"},{default:s(()=>[E(n(C,{link:"",type:"primary",icon:"Edit",onClick:ne=>f(o.row)},null,8,["onClick"]),[[H,["system:user:edit"]]])]),_:2},1024)):$("",!0),o.row.userId!==1?(h(),x(ye,{key:1,content:"删除",placement:"top"},{default:s(()=>[E(n(C,{link:"",type:"primary",icon:"Delete",onClick:ne=>_e(o.row)},null,8,["onClick"]),[[H,["system:user:remove"]]])]),_:2},1024)):$("",!0),o.row.userId!==1?(h(),x(ye,{key:2,content:"重置密码",placement:"top"},{default:s(()=>[E(n(C,{link:"",type:"primary",icon:"Key",onClick:ne=>Ie(o.row)},null,8,["onClick"]),[[H,["system:user:resetPwd"]]])]),_:2},1024)):$("",!0),o.row.userId!==1?(h(),x(ye,{key:3,content:"分配角色",placement:"top"},{default:s(()=>[E(n(C,{link:"",type:"primary",icon:"CircleCheck",onClick:ne=>Ne(o.row)},null,8,["onClick"]),[[H,["system:user:edit"]]])]),_:2},1024)):$("",!0)]),_:1})]),_:1},8,["data"])),[[al,i(V)]]),E(n(Je,{total:i(K),page:i(k).pageNum,"onUpdate:page":l[6]||(l[6]=o=>i(k).pageNum=o),limit:i(k).pageSize,"onUpdate:limit":l[7]||(l[7]=o=>i(k).pageSize=o),onPagination:F},null,8,["total","page","limit"]),[[Oe,i(K)>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),n($e,{title:i(j),modelValue:i(w),"onUpdate:modelValue":l[19]||(l[19]=o=>ze(w)?w.value=o:null),width:"600px","append-to-body":""},{footer:s(()=>[A("div",Sl,[n(C,{type:"primary",onClick:N},{default:s(()=>l[30]||(l[30]=[M("确 定")])),_:1,__:[30]}),n(C,{onClick:u},{default:s(()=>l[31]||(l[31]=[M("取 消")])),_:1,__:[31]})])]),default:s(()=>[n(Re,{model:i(c),rules:i(we),ref:"userRef","label-width":"80px"},{default:s(()=>[n(W,null,{default:s(()=>[n(S,{span:12},{default:s(()=>[n(I,{label:"用户昵称",prop:"nickName"},{default:s(()=>[n(z,{modelValue:i(c).nickName,"onUpdate:modelValue":l[8]||(l[8]=o=>i(c).nickName=o),placeholder:"请输入用户昵称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),n(S,{span:12},{default:s(()=>[n(I,{label:"归属部门",prop:"deptId"},{default:s(()=>[n(Qe,{modelValue:i(c).deptId,"onUpdate:modelValue":l[9]||(l[9]=o=>i(c).deptId=o),data:i(te),props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择归属部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1}),n(W,null,{default:s(()=>[n(S,{span:12},{default:s(()=>[n(I,{label:"手机号码",prop:"phonenumber"},{default:s(()=>[n(z,{modelValue:i(c).phonenumber,"onUpdate:modelValue":l[10]||(l[10]=o=>i(c).phonenumber=o),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),n(S,{span:12},{default:s(()=>[n(I,{label:"邮箱",prop:"email"},{default:s(()=>[n(z,{modelValue:i(c).email,"onUpdate:modelValue":l[11]||(l[11]=o=>i(c).email=o),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(W,null,{default:s(()=>[n(S,{span:12},{default:s(()=>[i(c).userId==null?(h(),x(I,{key:0,label:"用户名称",prop:"userName"},{default:s(()=>[n(z,{modelValue:i(c).userName,"onUpdate:modelValue":l[12]||(l[12]=o=>i(c).userName=o),placeholder:"请输入用户名称",maxlength:"30"},null,8,["modelValue"])]),_:1})):$("",!0)]),_:1}),n(S,{span:12},{default:s(()=>[i(c).userId==null?(h(),x(I,{key:0,label:"用户密码",prop:"password"},{default:s(()=>[n(z,{modelValue:i(c).password,"onUpdate:modelValue":l[13]||(l[13]=o=>i(c).password=o),placeholder:"请输入用户密码",type:"password",maxlength:"20","show-password":""},null,8,["modelValue"])]),_:1})):$("",!0)]),_:1})]),_:1}),n(W,null,{default:s(()=>[n(S,{span:12},{default:s(()=>[n(I,{label:"用户性别"},{default:s(()=>[n(be,{modelValue:i(c).sex,"onUpdate:modelValue":l[14]||(l[14]=o=>i(c).sex=o),placeholder:"请选择"},{default:s(()=>[(h(!0),ee(pe,null,me(i(m),o=>(h(),x(xe,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(S,{span:12},{default:s(()=>[n(I,{label:"状态"},{default:s(()=>[n(Xe,{modelValue:i(c).status,"onUpdate:modelValue":l[15]||(l[15]=o=>i(c).status=o)},{default:s(()=>[(h(!0),ee(pe,null,me(i(X),o=>(h(),x(We,{key:o.value,value:o.value},{default:s(()=>[M(Le(o.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(W,null,{default:s(()=>[n(S,{span:12},{default:s(()=>[n(I,{label:"岗位"},{default:s(()=>[n(be,{modelValue:i(c).postIds,"onUpdate:modelValue":l[16]||(l[16]=o=>i(c).postIds=o),multiple:"",placeholder:"请选择"},{default:s(()=>[(h(!0),ee(pe,null,me(i(ie),o=>(h(),x(xe,{key:o.postId,label:o.postName,value:o.postId,disabled:o.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(S,{span:12},{default:s(()=>[n(I,{label:"角色"},{default:s(()=>[n(be,{modelValue:i(c).roleIds,"onUpdate:modelValue":l[17]||(l[17]=o=>i(c).roleIds=o),multiple:"",placeholder:"请选择"},{default:s(()=>[(h(!0),ee(pe,null,me(i(ue),o=>(h(),x(xe,{key:o.roleId,label:o.roleName,value:o.roleId,disabled:o.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(W,null,{default:s(()=>[n(S,{span:24},{default:s(()=>[n(I,{label:"备注"},{default:s(()=>[n(z,{modelValue:i(c).remark,"onUpdate:modelValue":l[18]||(l[18]=o=>i(c).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),n($e,{title:i(P).title,modelValue:i(P).open,"onUpdate:modelValue":l[22]||(l[22]=o=>i(P).open=o),width:"400px","append-to-body":""},{footer:s(()=>[A("div",Nl,[n(C,{type:"primary",onClick:r},{default:s(()=>l[36]||(l[36]=[M("确 定")])),_:1,__:[36]}),n(C,{onClick:l[21]||(l[21]=o=>i(P).open=!1)},{default:s(()=>l[37]||(l[37]=[M("取 消")])),_:1,__:[37]})])]),default:s(()=>[n(tl,{ref:"uploadRef",limit:1,accept:".xlsx, .xls",headers:i(P).headers,action:i(P).url+"?updateSupport="+i(P).updateSupport,disabled:i(P).isUploading,"on-progress":e,"on-success":t,"auto-upload":!1,drag:""},{tip:s(()=>[A("div",Vl,[A("div",Pl,[n(el,{modelValue:i(P).updateSupport,"onUpdate:modelValue":l[20]||(l[20]=o=>i(P).updateSupport=o)},null,8,["modelValue"]),l[32]||(l[32]=M("是否更新已经存在的用户数据 "))]),l[34]||(l[34]=A("span",null,"仅允许导入xls、xlsx格式文件。",-1)),n(ll,{type:"primary",underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},onClick:Ce},{default:s(()=>l[33]||(l[33]=[M("下载模板")])),_:1,__:[33]})])]),default:s(()=>[n(Ze,{class:"el-icon--upload"},{default:s(()=>[n(Ge)]),_:1}),l[35]||(l[35]=A("div",{class:"el-upload__text"},[M("将文件拖到此处，或"),A("em",null,"点击上传")],-1))]),_:1,__:[35]},8,["headers","action","disabled"])]),_:1},8,["title","modelValue"])])}}});export{Ul as default};
