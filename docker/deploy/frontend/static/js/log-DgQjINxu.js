import{S as G,z as de,d as pe,r as b,u as me,A as _e,I as fe,e as r,F as Y,c as k,o as d,G as j,f as e,H as F,l as t,i as o,m as ce,J as K,K as Q,j as g,C as T,n as s,h as E,t as v,k as O}from"./index-CdiCNU81.js";import{g as be}from"./job-CBVoAt_P.js";function ge(V){return G({url:"/monitor/jobLog/list",method:"get",params:V})}function ve(V){return G({url:"/monitor/jobLog/"+V,method:"delete"})}function we(){return G({url:"/monitor/jobLog/clean",method:"delete"})}const je={class:"app-container"},ye={key:0},he={key:1},ke={class:"dialog-footer"},Ve=de({name:"JobLog"}),Se=Object.assign(Ve,{setup(V){const{proxy:_}=pe(),{sys_common_status:I,sys_job_group:R}=_.useDict("sys_common_status","sys_job_group"),U=b([]),y=b(!1),N=b(!0),C=b(!0),L=b([]),$=b(!0),S=b(0),h=b([]),J=me(),A=_e({form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0}}),{queryParams:n,form:p,rules:Ce}=fe(A);function w(){N.value=!0,ge(_.addDateRange(n.value,h.value)).then(u=>{U.value=u.rows,S.value=u.total,N.value=!1})}function H(){const u={path:"/monitor/job"};_.$tab.closeOpenPage(u)}function D(){n.value.pageNum=1,w()}function W(){h.value=[],_.resetForm("queryRef"),D()}function X(u){L.value=u.map(l=>l.jobLogId),$.value=!u.length}function Z(u){y.value=!0,p.value=u}function ee(u){_.$modal.confirm('是否确认删除调度日志编号为"'+L.value+'"的数据项?').then(function(){return ve(L.value)}).then(()=>{w(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){_.$modal.confirm("是否确认清空所有调度日志数据项?").then(function(){return we()}).then(()=>{w(),_.$modal.msgSuccess("清空成功")}).catch(()=>{})}function le(){_.download("monitor/jobLog/export",{...n.value},`job_log_${new Date().getTime()}.xlsx`)}return(()=>{const u=J.params&&J.params.jobId;u!==void 0&&u!=0?be(u).then(l=>{n.value.jobName=l.data.jobName,n.value.jobGroup=l.data.jobGroup,w()}):w()})(),(u,l)=>{const te=r("el-input"),i=r("el-form-item"),P=r("el-option"),q=r("el-select"),ae=r("el-date-picker"),c=r("el-button"),z=r("el-form"),m=r("el-col"),ne=r("right-toolbar"),B=r("el-row"),f=r("el-table-column"),M=r("dict-tag"),ue=r("el-table"),re=r("pagination"),se=r("el-dialog"),x=Y("hasPermi"),ie=Y("loading");return d(),k("div",je,[j(e(z,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[e(i,{label:"任务名称",prop:"jobName"},{default:o(()=>[e(te,{modelValue:t(n).jobName,"onUpdate:modelValue":l[0]||(l[0]=a=>t(n).jobName=a),placeholder:"请输入任务名称",clearable:"",style:{width:"240px"},onKeyup:ce(D,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"任务组名",prop:"jobGroup"},{default:o(()=>[e(q,{modelValue:t(n).jobGroup,"onUpdate:modelValue":l[1]||(l[1]=a=>t(n).jobGroup=a),placeholder:"请选择任务组名",clearable:"",style:{width:"240px"}},{default:o(()=>[(d(!0),k(K,null,Q(t(R),a=>(d(),g(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行状态",prop:"status"},{default:o(()=>[e(q,{modelValue:t(n).status,"onUpdate:modelValue":l[2]||(l[2]=a=>t(n).status=a),placeholder:"请选择执行状态",clearable:"",style:{width:"240px"}},{default:o(()=>[(d(!0),k(K,null,Q(t(I),a=>(d(),g(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行时间",style:{width:"308px"}},{default:o(()=>[e(ae,{modelValue:t(h),"onUpdate:modelValue":l[3]||(l[3]=a=>T(h)?h.value=a:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(i,null,{default:o(()=>[e(c,{type:"primary",icon:"Search",onClick:D},{default:o(()=>l[9]||(l[9]=[s("搜索")])),_:1,__:[9]}),e(c,{icon:"Refresh",onClick:W},{default:o(()=>l[10]||(l[10]=[s("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"]),[[F,t(C)]]),e(B,{gutter:10,class:"mb8"},{default:o(()=>[e(m,{span:1.5},{default:o(()=>[j((d(),g(c,{type:"danger",plain:"",icon:"Delete",disabled:t($),onClick:ee},{default:o(()=>l[11]||(l[11]=[s("删除")])),_:1,__:[11]},8,["disabled"])),[[x,["monitor:job:remove"]]])]),_:1}),e(m,{span:1.5},{default:o(()=>[j((d(),g(c,{type:"danger",plain:"",icon:"Delete",onClick:oe},{default:o(()=>l[12]||(l[12]=[s("清空")])),_:1,__:[12]})),[[x,["monitor:job:remove"]]])]),_:1}),e(m,{span:1.5},{default:o(()=>[j((d(),g(c,{type:"warning",plain:"",icon:"Download",onClick:le},{default:o(()=>l[13]||(l[13]=[s("导出")])),_:1,__:[13]})),[[x,["monitor:job:export"]]])]),_:1}),e(m,{span:1.5},{default:o(()=>[e(c,{type:"warning",plain:"",icon:"Close",onClick:H},{default:o(()=>l[14]||(l[14]=[s("关闭")])),_:1,__:[14]})]),_:1}),e(ne,{showSearch:t(C),"onUpdate:showSearch":l[4]||(l[4]=a=>T(C)?C.value=a:null),onQueryTable:w},null,8,["showSearch"])]),_:1}),j((d(),g(ue,{data:t(U),onSelectionChange:X},{default:o(()=>[e(f,{type:"selection",width:"55",align:"center"}),e(f,{label:"日志编号",width:"80",align:"center",prop:"jobLogId"}),e(f,{label:"任务名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}),e(f,{label:"任务组名",align:"center",prop:"jobGroup","show-overflow-tooltip":!0},{default:o(a=>[e(M,{options:t(R),value:a.row.jobGroup},null,8,["options","value"])]),_:1}),e(f,{label:"调用目标字符串",align:"center",prop:"invokeTarget","show-overflow-tooltip":!0}),e(f,{label:"日志信息",align:"center",prop:"jobMessage","show-overflow-tooltip":!0}),e(f,{label:"执行状态",align:"center",prop:"status"},{default:o(a=>[e(M,{options:t(I),value:a.row.status},null,8,["options","value"])]),_:1}),e(f,{label:"执行时间",align:"center",prop:"createTime",width:"180"},{default:o(a=>[E("span",null,v(u.parseTime(a.row.createTime)),1)]),_:1}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(a=>[j((d(),g(c,{link:"",type:"primary",icon:"View",onClick:xe=>Z(a.row)},{default:o(()=>l[15]||(l[15]=[s("详细")])),_:2,__:[15]},1032,["onClick"])),[[x,["monitor:job:query"]]])]),_:1})]),_:1},8,["data"])),[[ie,t(N)]]),j(e(re,{total:t(S),page:t(n).pageNum,"onUpdate:page":l[5]||(l[5]=a=>t(n).pageNum=a),limit:t(n).pageSize,"onUpdate:limit":l[6]||(l[6]=a=>t(n).pageSize=a),onPagination:w},null,8,["total","page","limit"]),[[F,t(S)>0]]),e(se,{title:"调度日志详细",modelValue:t(y),"onUpdate:modelValue":l[8]||(l[8]=a=>T(y)?y.value=a:null),width:"700px","append-to-body":""},{footer:o(()=>[E("div",ke,[e(c,{onClick:l[7]||(l[7]=a=>y.value=!1)},{default:o(()=>l[16]||(l[16]=[s("关 闭")])),_:1,__:[16]})])]),default:o(()=>[e(z,{model:t(p),"label-width":"100px"},{default:o(()=>[e(B,null,{default:o(()=>[e(m,{span:12},{default:o(()=>[e(i,{label:"日志序号："},{default:o(()=>[s(v(t(p).jobLogId),1)]),_:1}),e(i,{label:"任务名称："},{default:o(()=>[s(v(t(p).jobName),1)]),_:1})]),_:1}),e(m,{span:12},{default:o(()=>[e(i,{label:"任务分组："},{default:o(()=>[s(v(t(p).jobGroup),1)]),_:1}),e(i,{label:"执行时间："},{default:o(()=>[s(v(t(p).createTime),1)]),_:1})]),_:1}),e(m,{span:24},{default:o(()=>[e(i,{label:"调用方法："},{default:o(()=>[s(v(t(p).invokeTarget),1)]),_:1})]),_:1}),e(m,{span:24},{default:o(()=>[e(i,{label:"日志信息："},{default:o(()=>[s(v(t(p).jobMessage),1)]),_:1})]),_:1}),e(m,{span:24},{default:o(()=>[e(i,{label:"执行状态："},{default:o(()=>[t(p).status==0?(d(),k("div",ye,"正常")):t(p).status==1?(d(),k("div",he,"失败")):O("",!0)]),_:1})]),_:1}),e(m,{span:24},{default:o(()=>[t(p).status==1?(d(),g(i,{key:0,label:"异常信息："},{default:o(()=>[s(v(t(p).exceptionInfo),1)]),_:1})):O("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Se as default};
