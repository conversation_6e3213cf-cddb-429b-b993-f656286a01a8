import{r as i,e as m,j as b,o as p,i as l,f as e,l as c}from"./index-CdiCNU81.js";const N={__name:"basicInfoForm",props:{info:{type:Object,default:null}},setup(o){const d=i({tableName:[{required:!0,message:"请输入表名称",trigger:"blur"}],tableComment:[{required:!0,message:"请输入表描述",trigger:"blur"}],className:[{required:!0,message:"请输入实体类名称",trigger:"blur"}],functionAuthor:[{required:!0,message:"请输入作者",trigger:"blur"}]});return(V,a)=>{const n=m("el-input"),u=m("el-form-item"),r=m("el-col"),f=m("el-row"),s=m("el-form");return p(),b(s,{ref:"basicInfoForm",model:o.info,rules:c(d),"label-width":"150px"},{default:l(()=>[e(f,null,{default:l(()=>[e(r,{span:12},{default:l(()=>[e(u,{label:"表名称",prop:"tableName"},{default:l(()=>[e(n,{placeholder:"请输入仓库名称",modelValue:o.info.tableName,"onUpdate:modelValue":a[0]||(a[0]=t=>o.info.tableName=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(u,{label:"表描述",prop:"tableComment"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.tableComment,"onUpdate:modelValue":a[1]||(a[1]=t=>o.info.tableComment=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(u,{label:"实体类名称",prop:"className"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.className,"onUpdate:modelValue":a[2]||(a[2]=t=>o.info.className=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(u,{label:"作者",prop:"functionAuthor"},{default:l(()=>[e(n,{placeholder:"请输入",modelValue:o.info.functionAuthor,"onUpdate:modelValue":a[3]||(a[3]=t=>o.info.functionAuthor=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:24},{default:l(()=>[e(u,{label:"备注",prop:"remark"},{default:l(()=>[e(n,{type:"textarea",rows:3,modelValue:o.info.remark,"onUpdate:modelValue":a[4]||(a[4]=t=>o.info.remark=t)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}};export{N as default};
