import{z as Ue,d as Pe,a as De,r as c,A as xe,I as Se,e as m,F as Z,c as f,o,G as V,f as l,H as ee,i as a,m as K,l as n,J as b,K as y,j as i,n as v,t as z,h as he}from"./index-CdiCNU81.js";import{l as Le,g as Re,d as Ne,u as Ie,a as je}from"./application-DkzhB5es.js";const Oe={class:"app-container"},$e={class:"dialog-footer"},Ae=Ue({name:"Application"}),Fe=Object.assign(Ae,{setup(qe){const{proxy:w}=Pe(),le=De(),{sdl_user_type:ae,sdl_data_sensitivity:te,sdl_dev_type:ne,sdl_security_level:E,sdl_business_domian:h,sdl_dev_language:ue,sdl_business_importance:oe,sdl_app_type:L,sdl_system_dependency:pe,sdl_access_path:se}=w.useDict("sdl_user_type","sdl_data_sensitivity","sdl_dev_type","sdl_security_level","sdl_business_domian","sdl_dev_language","sdl_business_importance","sdl_app_type","sdl_system_dependency","sdl_access_path"),Y=c([]),P=c(!1),R=c(!0),N=c(!0),I=c([]),Q=c(!0),G=c(!0),j=c(0),O=c(""),$=c([]),de=xe({form:{},queryParams:{pageNum:1,pageSize:10,appName:null,appType:null,businessDomain:null,department:null,responsiblePerson:null,securityLevel:null,startCreateTime:null,endCreateTime:null},rules:{appName:[{required:!0,message:"应用名称不能为空",trigger:"blur"}]}}),{queryParams:s,form:u,rules:re}=Se(de);function ie(r){r&&r.length===2?(s.value.startCreateTime=r[0],s.value.endCreateTime=r[1]):(s.value.startCreateTime=null,s.value.endCreateTime=null)}function me(r){const t=r.appId;le.push({path:"/business/application/detail",query:{id:t}})}function D(){R.value=!0,Le(s.value).then(r=>{Y.value=r.rows,j.value=r.total,R.value=!1})}function ve(){P.value=!1,A()}function A(){u.value={appId:null,appName:null,appType:null,businessDomain:null,department:null,responsiblePerson:null,securityRepresentative:null,securityLevel:null,userType:[],developmentLanguage:[],developmentType:null,recoveryTimeObjective:null,recoveryPointObjective:null,accessPath:[],dataSensitivity:null,businessImportance:null,systemDependency:null,appMenbers:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},w.resetForm("applicationRef")}function S(){s.value.pageNum=1,D()}function _e(){$.value=[],s.value.startCreateTime=null,s.value.endCreateTime=null,w.resetForm("queryRef"),S()}function fe(r){I.value=r.map(t=>t.appId),Q.value=r.length!=1,G.value=!r.length}function be(){A(),P.value=!0,O.value="添加应用"}function H(r){A();const t=r.appId||I.value;Re(t).then(_=>{u.value=_.data,u.value.userType=u.value.userType.split(","),u.value.developmentLanguage=u.value.developmentLanguage.split(","),u.value.accessPath=u.value.accessPath.split(","),P.value=!0,O.value="修改应用"})}function ye(){w.$refs.applicationRef.validate(r=>{r&&(u.value.userType=u.value.userType.join(","),u.value.developmentLanguage=u.value.developmentLanguage.join(","),u.value.accessPath=u.value.accessPath.join(","),u.value.appId!=null?Ie(u.value).then(t=>{w.$modal.msgSuccess("修改成功"),P.value=!1,D()}):je(u.value).then(t=>{w.$modal.msgSuccess("新增成功"),P.value=!1,D()}))})}function J(r){const t=r.appId||I.value;w.$modal.confirm('是否确认删除应用编号为"'+t+'"的数据项？').then(function(){return Ne(t)}).then(()=>{D(),w.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ge(){w.download("business/application/export",{...s.value},`application_${new Date().getTime()}.xlsx`)}return D(),(r,t)=>{const _=m("el-input"),d=m("el-form-item"),p=m("el-col"),k=m("el-option"),T=m("el-select"),C=m("el-row"),ce=m("el-date-picker"),g=m("el-button"),W=m("el-form"),Ve=m("right-toolbar"),U=m("el-table-column"),q=m("dict-tag"),we=m("el-table"),ke=m("pagination"),B=m("el-divider"),M=m("el-checkbox"),F=m("el-checkbox-group"),Te=m("el-dialog"),x=Z("hasPermi"),Ce=Z("loading");return o(),f("div",Oe,[V(l(W,{model:n(s),ref:"queryRef","label-width":"80px"},{default:a(()=>[l(C,{gutter:20},{default:a(()=>[l(p,{span:6},{default:a(()=>[l(d,{label:"应用名称",prop:"appName"},{default:a(()=>[l(_,{modelValue:n(s).appName,"onUpdate:modelValue":t[0]||(t[0]=e=>n(s).appName=e),placeholder:"请输入应用名称",clearable:"",onKeyup:K(S,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{label:"应用类型",prop:"appType"},{default:a(()=>[l(T,{modelValue:n(s).appType,"onUpdate:modelValue":t[1]||(t[1]=e=>n(s).appType=e),placeholder:"请选择应用类型",clearable:"",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(L),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{label:"业务领域",prop:"businessDomain"},{default:a(()=>[l(T,{modelValue:n(s).businessDomain,"onUpdate:modelValue":t[2]||(t[2]=e=>n(s).businessDomain=e),placeholder:"请选择业务领域",clearable:"",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(h),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{label:"所属部门",prop:"department"},{default:a(()=>[l(_,{modelValue:n(s).department,"onUpdate:modelValue":t[3]||(t[3]=e=>n(s).department=e),placeholder:"请输入所属部门",clearable:"",onKeyup:K(S,["enter"])},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{gutter:20},{default:a(()=>[l(p,{span:6},{default:a(()=>[l(d,{label:"责任人",prop:"responsiblePerson"},{default:a(()=>[l(_,{modelValue:n(s).responsiblePerson,"onUpdate:modelValue":t[4]||(t[4]=e=>n(s).responsiblePerson=e),placeholder:"请输入责任人",clearable:"",onKeyup:K(S,["enter"])},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{label:"安全等级",prop:"securityLevel"},{default:a(()=>[l(T,{modelValue:n(s).securityLevel,"onUpdate:modelValue":t[5]||(t[5]=e=>n(s).securityLevel=e),placeholder:"请选择安全等级",clearable:"",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(E),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{label:"创建时间",prop:"dateRange"},{default:a(()=>[l(ce,{modelValue:$.value,"onUpdate:modelValue":t[6]||(t[6]=e=>$.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",onChange:ie},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:6},{default:a(()=>[l(d,{style:{"margin-bottom":"0px","margin-right":"0px"}},{default:a(()=>[l(g,{type:"primary",icon:"Search",onClick:S},{default:a(()=>t[28]||(t[28]=[v("搜索")])),_:1,__:[28]}),l(g,{icon:"Refresh",onClick:_e,style:{"margin-right":"5px"}},{default:a(()=>t[29]||(t[29]=[v("重置")])),_:1,__:[29]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),[[ee,N.value]]),l(C,{gutter:10,class:"mb8"},{default:a(()=>[l(p,{span:1.5},{default:a(()=>[V((o(),i(g,{type:"primary",plain:"",icon:"Plus",onClick:be},{default:a(()=>t[30]||(t[30]=[v("新增")])),_:1,__:[30]})),[[x,["business:application:add"]]])]),_:1}),l(p,{span:1.5},{default:a(()=>[V((o(),i(g,{type:"success",plain:"",icon:"Edit",disabled:Q.value,onClick:H},{default:a(()=>t[31]||(t[31]=[v("修改")])),_:1,__:[31]},8,["disabled"])),[[x,["business:application:edit"]]])]),_:1}),l(p,{span:1.5},{default:a(()=>[V((o(),i(g,{type:"danger",plain:"",icon:"Delete",disabled:G.value,onClick:J},{default:a(()=>t[32]||(t[32]=[v("删除")])),_:1,__:[32]},8,["disabled"])),[[x,["business:application:remove"]]])]),_:1}),l(p,{span:1.5},{default:a(()=>[V((o(),i(g,{type:"warning",plain:"",icon:"Download",onClick:ge},{default:a(()=>t[33]||(t[33]=[v("导出")])),_:1,__:[33]})),[[x,["business:application:export"]]])]),_:1}),l(Ve,{showSearch:N.value,"onUpdate:showSearch":t[7]||(t[7]=e=>N.value=e),onQueryTable:D},null,8,["showSearch"])]),_:1}),V((o(),i(we,{data:Y.value,onSelectionChange:fe,fit:"",style:{width:"100%"}},{default:a(()=>[l(U,{type:"selection",width:"55",align:"center"}),l(U,{label:"应用名称",align:"center",prop:"appName","show-overflow-tooltip":""}),l(U,{label:"应用类型",align:"center",prop:"appType"},{default:a(e=>[l(q,{options:n(L),value:e.row.appType},null,8,["options","value"])]),_:1}),l(U,{label:"业务领域",align:"center",prop:"businessDomain"},{default:a(e=>[l(q,{options:n(h),value:e.row.businessDomain},null,8,["options","value"])]),_:1}),l(U,{label:"所属部门",align:"center",prop:"department"}),l(U,{label:"责任人",align:"center",prop:"responsiblePerson"}),l(U,{label:"安全代表",align:"center",prop:"securityRepresentative"}),l(U,{label:"安全等级",align:"center",prop:"securityLevel"},{default:a(e=>[l(q,{options:n(E),value:e.row.securityLevel},null,8,["options","value"])]),_:1}),l(U,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},{default:a(e=>[V((o(),i(g,{link:"",type:"primary",icon:"View",onClick:X=>me(e.row)},{default:a(()=>t[34]||(t[34]=[v("查看")])),_:2,__:[34]},1032,["onClick"])),[[x,["business:application:query"]]]),V((o(),i(g,{link:"",type:"primary",icon:"Edit",onClick:X=>H(e.row)},{default:a(()=>t[35]||(t[35]=[v("修改")])),_:2,__:[35]},1032,["onClick"])),[[x,["business:application:edit"]]]),V((o(),i(g,{link:"",type:"primary",icon:"Delete",onClick:X=>J(e.row)},{default:a(()=>t[36]||(t[36]=[v("删除")])),_:2,__:[36]},1032,["onClick"])),[[x,["business:application:remove"]]])]),_:1})]),_:1},8,["data"])),[[Ce,R.value]]),V(l(ke,{total:j.value,page:n(s).pageNum,"onUpdate:page":t[8]||(t[8]=e=>n(s).pageNum=e),limit:n(s).pageSize,"onUpdate:limit":t[9]||(t[9]=e=>n(s).pageSize=e),onPagination:D},null,8,["total","page","limit"]),[[ee,j.value>0]]),l(Te,{title:O.value,modelValue:P.value,"onUpdate:modelValue":t[27]||(t[27]=e=>P.value=e),width:"800px","append-to-body":""},{footer:a(()=>[he("div",$e,[l(g,{type:"primary",onClick:ye},{default:a(()=>t[40]||(t[40]=[v("确 定")])),_:1,__:[40]}),l(g,{onClick:ve},{default:a(()=>t[41]||(t[41]=[v("取 消")])),_:1,__:[41]})])]),default:a(()=>[l(W,{ref:"applicationRef",model:n(u),rules:n(re),"label-width":"100px"},{default:a(()=>[l(B,{"content-position":"left"},{default:a(()=>t[37]||(t[37]=[v("基本信息")])),_:1,__:[37]}),l(C,{gutter:20},{default:a(()=>[l(p,{span:8},{default:a(()=>[l(d,{label:"应用名称",prop:"appName"},{default:a(()=>[l(_,{modelValue:n(u).appName,"onUpdate:modelValue":t[10]||(t[10]=e=>n(u).appName=e),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"所属部门",prop:"department"},{default:a(()=>[l(_,{modelValue:n(u).department,"onUpdate:modelValue":t[11]||(t[11]=e=>n(u).department=e),placeholder:"请输入所属部门"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"责任人",prop:"responsiblePerson"},{default:a(()=>[l(_,{modelValue:n(u).responsiblePerson,"onUpdate:modelValue":t[12]||(t[12]=e=>n(u).responsiblePerson=e),placeholder:"请输入责任人"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"应用类型",prop:"appType"},{default:a(()=>[l(T,{modelValue:n(u).appType,"onUpdate:modelValue":t[13]||(t[13]=e=>n(u).appType=e),placeholder:"请选择应用类型",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(L),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"业务领域",prop:"businessDomain"},{default:a(()=>[l(T,{modelValue:n(u).businessDomain,"onUpdate:modelValue":t[14]||(t[14]=e=>n(u).businessDomain=e),placeholder:"请选择业务领域",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(h),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"安全代表",prop:"securityRepresentative"},{default:a(()=>[l(_,{modelValue:n(u).securityRepresentative,"onUpdate:modelValue":t[15]||(t[15]=e=>n(u).securityRepresentative=e),placeholder:"请输入安全代表"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"开发类型",prop:"developmentType"},{default:a(()=>[l(T,{modelValue:n(u).developmentType,"onUpdate:modelValue":t[16]||(t[16]=e=>n(u).developmentType=e),placeholder:"请选择开发类型",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(ne),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:16},{default:a(()=>[l(d,{label:"应用成员",prop:"appMenbers"},{default:a(()=>[l(_,{modelValue:n(u).appMenbers,"onUpdate:modelValue":t[17]||(t[17]=e=>n(u).appMenbers=e),placeholder:"请输入应用成员，多个成员用逗号分隔"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(B,{"content-position":"left"},{default:a(()=>t[38]||(t[38]=[v("定级信息")])),_:1,__:[38]}),l(C,{gutter:20},{default:a(()=>[l(p,{span:20},{default:a(()=>[l(d,{label:"用户类型",prop:"userType"},{default:a(()=>[l(F,{modelValue:n(u).userType,"onUpdate:modelValue":t[18]||(t[18]=e=>n(u).userType=e)},{default:a(()=>[(o(!0),f(b,null,y(n(ae),e=>(o(),i(M,{key:e.value,label:e.value},{default:a(()=>[v(z(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:20},{default:a(()=>[l(d,{label:"访问路径",prop:"accessPath"},{default:a(()=>[l(F,{modelValue:n(u).accessPath,"onUpdate:modelValue":t[19]||(t[19]=e=>n(u).accessPath=e)},{default:a(()=>[(o(!0),f(b,null,y(n(se),e=>(o(),i(M,{key:e.value,label:e.value},{default:a(()=>[v(z(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"数据敏感度",prop:"dataSensitivity"},{default:a(()=>[l(T,{modelValue:n(u).dataSensitivity,"onUpdate:modelValue":t[20]||(t[20]=e=>n(u).dataSensitivity=e),placeholder:"请选择数据敏感度",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(te),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"业务重要性",prop:"businessImportance"},{default:a(()=>[l(T,{modelValue:n(u).businessImportance,"onUpdate:modelValue":t[21]||(t[21]=e=>n(u).businessImportance=e),placeholder:"请选择业务重要性",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(oe),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(p,{span:8},{default:a(()=>[l(d,{label:"系统依赖性",prop:"systemDependency"},{default:a(()=>[l(T,{modelValue:n(u).systemDependency,"onUpdate:modelValue":t[22]||(t[22]=e=>n(u).systemDependency=e),placeholder:"请选择系统依赖性",style:{width:"100%"}},{default:a(()=>[(o(!0),f(b,null,y(n(pe),e=>(o(),i(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(B,{"content-position":"left"},{default:a(()=>t[39]||(t[39]=[v("其它信息")])),_:1,__:[39]}),l(C,{gutter:20}),l(C,{gutter:20},{default:a(()=>[l(p,{span:24},{default:a(()=>[l(d,{label:"开发语言",prop:"developmentLanguage"},{default:a(()=>[l(F,{modelValue:n(u).developmentLanguage,"onUpdate:modelValue":t[23]||(t[23]=e=>n(u).developmentLanguage=e)},{default:a(()=>[(o(!0),f(b,null,y(n(ue),e=>(o(),i(M,{key:e.value,label:e.value},{default:a(()=>[v(z(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{gutter:20},{default:a(()=>[l(p,{span:12},{default:a(()=>[l(d,{label:"RTO",prop:"recoveryTimeObjective"},{default:a(()=>[l(_,{modelValue:n(u).recoveryTimeObjective,"onUpdate:modelValue":t[24]||(t[24]=e=>n(u).recoveryTimeObjective=e),placeholder:"请输入恢复时间目标"},null,8,["modelValue"])]),_:1})]),_:1}),l(p,{span:12},{default:a(()=>[l(d,{label:"RPO",prop:"recoveryPointObjective"},{default:a(()=>[l(_,{modelValue:n(u).recoveryPointObjective,"onUpdate:modelValue":t[25]||(t[25]=e=>n(u).recoveryPointObjective=e),placeholder:"请输入恢复时间点目标"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(C,{gutter:20},{default:a(()=>[l(p,{span:24},{default:a(()=>[l(d,{label:"备注",prop:"remark"},{default:a(()=>[l(_,{modelValue:n(u).remark,"onUpdate:modelValue":t[26]||(t[26]=e=>n(u).remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Fe as default};
