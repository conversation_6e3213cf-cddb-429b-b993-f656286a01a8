import{z as G,u as H,d as J,r as c,A as M,e as r,F as V,c as W,o as g,G as m,f as o,H as $,l as t,i as l,m as x,n as _,j as w,C as X,h as Y,t as Z}from"./index-CdiCNU81.js";import ee from"./selectUser-B00KGOSj.js";import{a as oe,b as te,c as le}from"./role-DqtqZXxX.js";const ne={class:"app-container"},ae=G({name:"AuthUser"}),de=Object.assign(ae,{setup(se){const R=H(),{proxy:i}=J(),{sys_normal_disable:A}=i.useDict("sys_normal_disable"),N=c([]),y=c(!0),h=c(!0),k=c(!0),v=c(0),U=c([]),a=M({pageNum:1,pageSize:10,roleId:R.params.roleId,userName:void 0,phonenumber:void 0});function p(){y.value=!0,oe(a).then(s=>{N.value=s.rows,v.value=s.total,y.value=!1})}function P(){const s={path:"/system/role"};i.$tab.closeOpenPage(s)}function f(){a.pageNum=1,p()}function T(){i.resetForm("queryRef"),f()}function j(s){U.value=s.map(e=>e.userId),k.value=!s.length}function z(){i.$refs.selectRef.show()}function B(s){i.$modal.confirm('确认要取消该用户"'+s.userName+'"角色吗？').then(function(){return le({userId:s.userId,roleId:a.roleId})}).then(()=>{p(),i.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}function D(s){const e=a.roleId,b=U.value.join(",");i.$modal.confirm("是否取消选中用户授权数据项?").then(function(){return te({roleId:e,userIds:b})}).then(()=>{p(),i.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}return p(),(s,e)=>{const b=r("el-input"),C=r("el-form-item"),d=r("el-button"),q=r("el-form"),I=r("el-col"),K=r("right-toolbar"),L=r("el-row"),u=r("el-table-column"),O=r("dict-tag"),Q=r("el-table"),F=r("pagination"),S=V("hasPermi"),E=V("loading");return g(),W("div",ne,[m(o(q,{model:t(a),ref:"queryRef",inline:!0},{default:l(()=>[o(C,{label:"用户名称",prop:"userName"},{default:l(()=>[o(b,{modelValue:t(a).userName,"onUpdate:modelValue":e[0]||(e[0]=n=>t(a).userName=n),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:x(f,["enter"])},null,8,["modelValue"])]),_:1}),o(C,{label:"手机号码",prop:"phonenumber"},{default:l(()=>[o(b,{modelValue:t(a).phonenumber,"onUpdate:modelValue":e[1]||(e[1]=n=>t(a).phonenumber=n),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:x(f,["enter"])},null,8,["modelValue"])]),_:1}),o(C,null,{default:l(()=>[o(d,{type:"primary",icon:"Search",onClick:f},{default:l(()=>e[5]||(e[5]=[_("搜索")])),_:1,__:[5]}),o(d,{icon:"Refresh",onClick:T},{default:l(()=>e[6]||(e[6]=[_("重置")])),_:1,__:[6]})]),_:1})]),_:1},8,["model"]),[[$,t(h)]]),o(L,{gutter:10,class:"mb8"},{default:l(()=>[o(I,{span:1.5},{default:l(()=>[m((g(),w(d,{type:"primary",plain:"",icon:"Plus",onClick:z},{default:l(()=>e[7]||(e[7]=[_("添加用户")])),_:1,__:[7]})),[[S,["system:role:add"]]])]),_:1}),o(I,{span:1.5},{default:l(()=>[m((g(),w(d,{type:"danger",plain:"",icon:"CircleClose",disabled:t(k),onClick:D},{default:l(()=>e[8]||(e[8]=[_("批量取消授权")])),_:1,__:[8]},8,["disabled"])),[[S,["system:role:remove"]]])]),_:1}),o(I,{span:1.5},{default:l(()=>[o(d,{type:"warning",plain:"",icon:"Close",onClick:P},{default:l(()=>e[9]||(e[9]=[_("关闭")])),_:1,__:[9]})]),_:1}),o(K,{showSearch:t(h),"onUpdate:showSearch":e[2]||(e[2]=n=>X(h)?h.value=n:null),onQueryTable:p},null,8,["showSearch"])]),_:1}),m((g(),w(Q,{data:t(N),onSelectionChange:j},{default:l(()=>[o(u,{type:"selection",width:"55",align:"center"}),o(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),o(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),o(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),o(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),o(u,{label:"状态",align:"center",prop:"status"},{default:l(n=>[o(O,{options:t(A),value:n.row.status},null,8,["options","value"])]),_:1}),o(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(n=>[Y("span",null,Z(s.parseTime(n.row.createTime)),1)]),_:1}),o(u,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(n=>[m((g(),w(d,{link:"",type:"primary",icon:"CircleClose",onClick:re=>B(n.row)},{default:l(()=>e[10]||(e[10]=[_("取消授权")])),_:2,__:[10]},1032,["onClick"])),[[S,["system:role:remove"]]])]),_:1})]),_:1},8,["data"])),[[E,t(y)]]),m(o(F,{total:t(v),page:t(a).pageNum,"onUpdate:page":e[3]||(e[3]=n=>t(a).pageNum=n),limit:t(a).pageSize,"onUpdate:limit":e[4]||(e[4]=n=>t(a).pageSize=n),onPagination:p},null,8,["total","page","limit"]),[[$,t(v)>0]]),o(t(ee),{ref:"selectRef",roleId:t(a).roleId,onOk:f},null,8,["roleId"])])}}});export{de as default};
