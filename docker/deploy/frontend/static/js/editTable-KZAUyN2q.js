import{g as L,u as q}from"./gen-COW18MkV.js";import{o as R}from"./type-7vf3hVgM.js";import P from"./basicInfoForm-ttwYDPve.js";import S from"./genInfoForm-CWN0Mj5z.js";import{z as G,u as O,d as K,r as c,B as M,T as Q,e as m,j as x,o as g,i as t,f as e,C as z,l as i,c as H,J,K as W,h,t as E,n as N}from"./index-CdiCNU81.js";import"./menu-BcsgTeEp.js";const A={style:{float:"left"}},X={style:{float:"right",color:"#8492a6","font-size":"13px"}},Y={style:{"text-align":"center","margin-left":"-100px","margin-top":"10px"}},Z=G({name:"GenEdit"}),de=Object.assign(Z,{setup(ee){const w=O(),{proxy:f}=K(),V=c("columnInfo"),C=c(document.documentElement.scrollHeight-245+"px"),y=c([]),b=c([]),U=c([]),s=c({});function B(){const d=f.$refs.basicInfo.$refs.basicInfoForm,n=f.$refs.genInfo.$refs.genInfoForm;Promise.all([d,n].map(D)).then(r=>{if(r.every(p=>!!p)){const p=Object.assign({},s.value);p.columns=b.value,p.params={treeCode:s.value.treeCode,treeName:s.value.treeName,treeParentCode:s.value.treeParentCode,parentMenuId:s.value.parentMenuId},q(p).then(a=>{f.$modal.msgSuccess(a.msg),a.code===200&&I()})}else f.$modal.msgError("表单校验未通过，请重新检查提交内容")})}function D(d){return new Promise(n=>{d.validate(r=>{n(r)})})}function I(){const d={path:"/tool/gen",query:{t:Date.now(),pageNum:w.query.pageNum}};f.$tab.closeOpenPage(d)}return(()=>{const d=w.params&&w.params.tableId;d&&(L(d).then(n=>{b.value=n.data.rows,s.value=n.data.info,y.value=n.data.tables}),R().then(n=>{U.value=n.data}))})(),M(()=>{const d=document.querySelector(".el-table__body > tbody");Q.create(d,{handle:".allowDrag",onEnd:n=>{const r=b.value.splice(n.oldIndex,1)[0];b.value.splice(n.newIndex,0,r);for(const u in b.value)b.value[u].sort=parseInt(u)+1}})}),(d,n)=>{const r=m("el-tab-pane"),u=m("el-table-column"),p=m("el-input"),a=m("el-option"),v=m("el-select"),_=m("el-checkbox"),$=m("el-table"),k=m("el-tabs"),T=m("el-button"),j=m("el-form"),F=m("el-card");return g(),x(F,null,{default:t(()=>[e(k,{modelValue:i(V),"onUpdate:modelValue":n[0]||(n[0]=l=>z(V)?V.value=l:null)},{default:t(()=>[e(r,{label:"基本信息",name:"basic"},{default:t(()=>[e(i(P),{ref:"basicInfo",info:i(s)},null,8,["info"])]),_:1}),e(r,{label:"字段信息",name:"columnInfo"},{default:t(()=>[e($,{ref:"dragTable",data:i(b),"row-key":"columnId","max-height":i(C)},{default:t(()=>[e(u,{label:"序号",type:"index","min-width":"5%","class-name":"allowDrag"}),e(u,{label:"字段列名",prop:"columnName","min-width":"10%","show-overflow-tooltip":!0,"class-name":"allowDrag"}),e(u,{label:"字段描述","min-width":"10%"},{default:t(l=>[e(p,{modelValue:l.row.columnComment,"onUpdate:modelValue":o=>l.row.columnComment=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"物理类型",prop:"columnType","min-width":"10%","show-overflow-tooltip":!0}),e(u,{label:"Java类型","min-width":"11%"},{default:t(l=>[e(v,{modelValue:l.row.javaType,"onUpdate:modelValue":o=>l.row.javaType=o},{default:t(()=>[e(a,{label:"Long",value:"Long"}),e(a,{label:"String",value:"String"}),e(a,{label:"Integer",value:"Integer"}),e(a,{label:"Double",value:"Double"}),e(a,{label:"BigDecimal",value:"BigDecimal"}),e(a,{label:"Date",value:"Date"}),e(a,{label:"Boolean",value:"Boolean"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"java属性","min-width":"10%"},{default:t(l=>[e(p,{modelValue:l.row.javaField,"onUpdate:modelValue":o=>l.row.javaField=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"插入","min-width":"5%"},{default:t(l=>[e(_,{"true-label":"1","false-value":"0",modelValue:l.row.isInsert,"onUpdate:modelValue":o=>l.row.isInsert=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"编辑","min-width":"5%"},{default:t(l=>[e(_,{"true-label":"1","false-value":"0",modelValue:l.row.isEdit,"onUpdate:modelValue":o=>l.row.isEdit=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"列表","min-width":"5%"},{default:t(l=>[e(_,{"true-label":"1","false-value":"0",modelValue:l.row.isList,"onUpdate:modelValue":o=>l.row.isList=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"查询","min-width":"5%"},{default:t(l=>[e(_,{"true-label":"1","false-value":"0",modelValue:l.row.isQuery,"onUpdate:modelValue":o=>l.row.isQuery=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"查询方式","min-width":"10%"},{default:t(l=>[e(v,{modelValue:l.row.queryType,"onUpdate:modelValue":o=>l.row.queryType=o},{default:t(()=>[e(a,{label:"=",value:"EQ"}),e(a,{label:"!=",value:"NE"}),e(a,{label:">",value:"GT"}),e(a,{label:">=",value:"GTE"}),e(a,{label:"<",value:"LT"}),e(a,{label:"<=",value:"LTE"}),e(a,{label:"LIKE",value:"LIKE"}),e(a,{label:"BETWEEN",value:"BETWEEN"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"必填","min-width":"5%"},{default:t(l=>[e(_,{"true-label":"1","false-label":"0",modelValue:l.row.isRequired,"onUpdate:modelValue":o=>l.row.isRequired=o},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"显示类型","min-width":"12%"},{default:t(l=>[e(v,{modelValue:l.row.htmlType,"onUpdate:modelValue":o=>l.row.htmlType=o},{default:t(()=>[e(a,{label:"文本框",value:"input"}),e(a,{label:"文本域",value:"textarea"}),e(a,{label:"下拉框",value:"select"}),e(a,{label:"单选框",value:"radio"}),e(a,{label:"复选框",value:"checkbox"}),e(a,{label:"日期控件",value:"datetime"}),e(a,{label:"图片上传",value:"imageUpload"}),e(a,{label:"文件上传",value:"fileUpload"}),e(a,{label:"富文本控件",value:"editor"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(u,{label:"字典类型","min-width":"12%"},{default:t(l=>[e(v,{modelValue:l.row.dictType,"onUpdate:modelValue":o=>l.row.dictType=o,clearable:"",filterable:"",placeholder:"请选择"},{default:t(()=>[(g(!0),H(J,null,W(i(U),o=>(g(),x(a,{key:o.dictType,label:o.dictName,value:o.dictType},{default:t(()=>[h("span",A,E(o.dictName),1),h("span",X,E(o.dictType),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data","max-height"])]),_:1}),e(r,{label:"生成信息",name:"genInfo"},{default:t(()=>[e(i(S),{ref:"genInfo",info:i(s),tables:i(y)},null,8,["info","tables"])]),_:1})]),_:1},8,["modelValue"]),e(j,{"label-width":"100px"},{default:t(()=>[h("div",Y,[e(T,{type:"primary",onClick:n[1]||(n[1]=l=>B())},{default:t(()=>n[3]||(n[3]=[N("提交")])),_:1,__:[3]}),e(T,{onClick:n[2]||(n[2]=l=>I())},{default:t(()=>n[4]||(n[4]=[N("返回")])),_:1,__:[4]})])]),_:1})]),_:1})}}});export{de as default};
