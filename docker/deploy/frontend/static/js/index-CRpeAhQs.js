import{S as k,z as ce,d as ge,r as c,A as ve,I as be,e as n,F as K,c as $,o as d,G as g,f as t,H as L,l as o,i as a,m as Q,J as j,K as A,j as v,n as p,C as G,h as H,t as J}from"./index-CdiCNU81.js";function ye(i){return k({url:"/system/post/list",method:"get",params:i})}function Ve(i){return k({url:"/system/post/"+i,method:"get"})}function Ce(i){return k({url:"/system/post",method:"post",data:i})}function we(i){return k({url:"/system/post",method:"put",data:i})}function ke(i){return k({url:"/system/post/"+i,method:"delete"})}const Se={class:"app-container"},Ne={class:"dialog-footer"},he=ce({name:"Post"}),Ue=Object.assign(he,{setup(i){const{proxy:f}=ge(),{sys_normal_disable:x}=f.useDict("sys_normal_disable"),q=c([]),b=c(!1),U=c(!0),S=c(!0),P=c([]),T=c(!0),F=c(!0),I=c(0),D=c(""),O=ve({form:{},queryParams:{pageNum:1,pageSize:10,postCode:void 0,postName:void 0,status:void 0},rules:{postName:[{required:!0,message:"岗位名称不能为空",trigger:"blur"}],postCode:[{required:!0,message:"岗位编码不能为空",trigger:"blur"}],postSort:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}]}}),{queryParams:u,form:s,rules:M}=be(O);function V(){U.value=!0,ye(u.value).then(r=>{q.value=r.rows,I.value=r.total,U.value=!1})}function W(){b.value=!1,R()}function R(){s.value={postId:void 0,postCode:void 0,postName:void 0,postSort:0,status:"0",remark:void 0},f.resetForm("postRef")}function N(){u.value.pageNum=1,V()}function X(){f.resetForm("queryRef"),N()}function Y(r){P.value=r.map(e=>e.postId),T.value=r.length!=1,F.value=!r.length}function Z(){R(),b.value=!0,D.value="添加岗位"}function z(r){R();const e=r.postId||P.value;Ve(e).then(C=>{s.value=C.data,b.value=!0,D.value="修改岗位"})}function ee(){f.$refs.postRef.validate(r=>{r&&(s.value.postId!=null?we(s.value).then(e=>{f.$modal.msgSuccess("修改成功"),b.value=!1,V()}):Ce(s.value).then(e=>{f.$modal.msgSuccess("新增成功"),b.value=!1,V()}))})}function B(r){const e=r.postId||P.value;f.$modal.confirm('是否确认删除岗位编号为"'+e+'"的数据项？').then(function(){return ke(e)}).then(()=>{V(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function te(){f.download("system/post/export",{...u.value},`post_${new Date().getTime()}.xlsx`)}return V(),(r,e)=>{const C=n("el-input"),_=n("el-form-item"),le=n("el-option"),oe=n("el-select"),m=n("el-button"),E=n("el-form"),h=n("el-col"),ae=n("right-toolbar"),ne=n("el-row"),y=n("el-table-column"),se=n("dict-tag"),ue=n("el-table"),re=n("pagination"),de=n("el-input-number"),pe=n("el-radio"),ie=n("el-radio-group"),me=n("el-dialog"),w=K("hasPermi"),fe=K("loading");return d(),$("div",Se,[g(t(E,{model:o(u),ref:"queryRef",inline:!0},{default:a(()=>[t(_,{label:"岗位编码",prop:"postCode"},{default:a(()=>[t(C,{modelValue:o(u).postCode,"onUpdate:modelValue":e[0]||(e[0]=l=>o(u).postCode=l),placeholder:"请输入岗位编码",clearable:"",style:{width:"200px"},onKeyup:Q(N,["enter"])},null,8,["modelValue"])]),_:1}),t(_,{label:"岗位名称",prop:"postName"},{default:a(()=>[t(C,{modelValue:o(u).postName,"onUpdate:modelValue":e[1]||(e[1]=l=>o(u).postName=l),placeholder:"请输入岗位名称",clearable:"",style:{width:"200px"},onKeyup:Q(N,["enter"])},null,8,["modelValue"])]),_:1}),t(_,{label:"状态",prop:"status"},{default:a(()=>[t(oe,{modelValue:o(u).status,"onUpdate:modelValue":e[2]||(e[2]=l=>o(u).status=l),placeholder:"岗位状态",clearable:"",style:{width:"200px"}},{default:a(()=>[(d(!0),$(j,null,A(o(x),l=>(d(),v(le,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(_,null,{default:a(()=>[t(m,{type:"primary",icon:"Search",onClick:N},{default:a(()=>e[12]||(e[12]=[p("搜索")])),_:1,__:[12]}),t(m,{icon:"Refresh",onClick:X},{default:a(()=>e[13]||(e[13]=[p("重置")])),_:1,__:[13]})]),_:1})]),_:1},8,["model"]),[[L,o(S)]]),t(ne,{gutter:10,class:"mb8"},{default:a(()=>[t(h,{span:1.5},{default:a(()=>[g((d(),v(m,{type:"primary",plain:"",icon:"Plus",onClick:Z},{default:a(()=>e[14]||(e[14]=[p("新增")])),_:1,__:[14]})),[[w,["system:post:add"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[g((d(),v(m,{type:"success",plain:"",icon:"Edit",disabled:o(T),onClick:z},{default:a(()=>e[15]||(e[15]=[p("修改")])),_:1,__:[15]},8,["disabled"])),[[w,["system:post:edit"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[g((d(),v(m,{type:"danger",plain:"",icon:"Delete",disabled:o(F),onClick:B},{default:a(()=>e[16]||(e[16]=[p("删除")])),_:1,__:[16]},8,["disabled"])),[[w,["system:post:remove"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[g((d(),v(m,{type:"warning",plain:"",icon:"Download",onClick:te},{default:a(()=>e[17]||(e[17]=[p("导出")])),_:1,__:[17]})),[[w,["system:post:export"]]])]),_:1}),t(ae,{showSearch:o(S),"onUpdate:showSearch":e[3]||(e[3]=l=>G(S)?S.value=l:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),g((d(),v(ue,{data:o(q),onSelectionChange:Y},{default:a(()=>[t(y,{type:"selection",width:"55",align:"center"}),t(y,{label:"岗位编号",align:"center",prop:"postId"}),t(y,{label:"岗位编码",align:"center",prop:"postCode"}),t(y,{label:"岗位名称",align:"center",prop:"postName"}),t(y,{label:"岗位排序",align:"center",prop:"postSort"}),t(y,{label:"状态",align:"center",prop:"status"},{default:a(l=>[t(se,{options:o(x),value:l.row.status},null,8,["options","value"])]),_:1}),t(y,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(l=>[H("span",null,J(r.parseTime(l.row.createTime)),1)]),_:1}),t(y,{label:"操作",width:"180",align:"center","class-name":"small-padding fixed-width"},{default:a(l=>[g((d(),v(m,{link:"",type:"primary",icon:"Edit",onClick:_e=>z(l.row)},{default:a(()=>e[18]||(e[18]=[p("修改")])),_:2,__:[18]},1032,["onClick"])),[[w,["system:post:edit"]]]),g((d(),v(m,{link:"",type:"primary",icon:"Delete",onClick:_e=>B(l.row)},{default:a(()=>e[19]||(e[19]=[p("删除")])),_:2,__:[19]},1032,["onClick"])),[[w,["system:post:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,o(U)]]),g(t(re,{total:o(I),page:o(u).pageNum,"onUpdate:page":e[4]||(e[4]=l=>o(u).pageNum=l),limit:o(u).pageSize,"onUpdate:limit":e[5]||(e[5]=l=>o(u).pageSize=l),onPagination:V},null,8,["total","page","limit"]),[[L,o(I)>0]]),t(me,{title:o(D),modelValue:o(b),"onUpdate:modelValue":e[11]||(e[11]=l=>G(b)?b.value=l:null),width:"500px","append-to-body":""},{footer:a(()=>[H("div",Ne,[t(m,{type:"primary",onClick:ee},{default:a(()=>e[20]||(e[20]=[p("确 定")])),_:1,__:[20]}),t(m,{onClick:W},{default:a(()=>e[21]||(e[21]=[p("取 消")])),_:1,__:[21]})])]),default:a(()=>[t(E,{ref:"postRef",model:o(s),rules:o(M),"label-width":"80px"},{default:a(()=>[t(_,{label:"岗位名称",prop:"postName"},{default:a(()=>[t(C,{modelValue:o(s).postName,"onUpdate:modelValue":e[6]||(e[6]=l=>o(s).postName=l),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),t(_,{label:"岗位编码",prop:"postCode"},{default:a(()=>[t(C,{modelValue:o(s).postCode,"onUpdate:modelValue":e[7]||(e[7]=l=>o(s).postCode=l),placeholder:"请输入编码名称"},null,8,["modelValue"])]),_:1}),t(_,{label:"岗位顺序",prop:"postSort"},{default:a(()=>[t(de,{modelValue:o(s).postSort,"onUpdate:modelValue":e[8]||(e[8]=l=>o(s).postSort=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),t(_,{label:"岗位状态",prop:"status"},{default:a(()=>[t(ie,{modelValue:o(s).status,"onUpdate:modelValue":e[9]||(e[9]=l=>o(s).status=l)},{default:a(()=>[(d(!0),$(j,null,A(o(x),l=>(d(),v(pe,{key:l.value,value:l.value},{default:a(()=>[p(J(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"备注",prop:"remark"},{default:a(()=>[t(C,{modelValue:o(s).remark,"onUpdate:modelValue":e[10]||(e[10]=l=>o(s).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ue as default};
