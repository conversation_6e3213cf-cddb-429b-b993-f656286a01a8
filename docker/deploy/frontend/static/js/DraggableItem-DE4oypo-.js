import{z as j,a9 as h,e as a,r as k,w as T,j as p,o as f,i as d,h as y,l as v,p as g,L as C,f as b,t as F}from"./index-CdiCNU81.js";import{m as O,d as L}from"./index-DL_ceqJA.js";const A=O("accept,accept-charset,accesskey,action,align,alt,async,autocomplete,autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,name,contenteditable,contextmenu,controls,coords,data,datetime,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,type,usemap,value,width,wrapprefix-icon"),B=O("layout,prepend,regList,tag,document,changeTag,defaultValue"),E={"el-button":{default(e,t,n){return t[n]}},"el-select":{options(e,t,n){return t.options.map(l=>e(a("el-option"),{label:l.label,value:l.value}))}},"el-radio-group":{options(e,t,n){return t.optionType==="button"?t.options.map(l=>e(a("el-checkbox-button"),{label:l.value},()=>l.label)):t.options.map(l=>e(a("el-radio"),{label:l.value,border:t.border},()=>l.label))}},"el-checkbox-group":{options(e,t,n){return t.optionType==="button"?t.options.map(l=>e(a("el-checkbox-button"),{label:l.value},()=>l.label)):t.options.map(l=>e(a("el-checkbox"),{label:l.value,border:t.border},()=>l.label))}},"el-upload":{"list-type":(e,t,n)=>{const l={};return t["list-type"]==="picture-card"?e(a("el-icon"),l,()=>e(a("Plus"))):(l.type="primary",l.icon="Upload",e(a("el-button"),l,()=>t.buttonText))}}},P={"el-upload":{tip:(e,t,n)=>{if(t.showTip)return()=>e("div",{class:"el-upload__tip"},"只能上传不超过"+t.fileSize+t.sizeUnit+"的"+t.accept+"文件")}}},U=j({render(){const e={attrs:{},props:{},on:{},style:{}},t=JSON.parse(JSON.stringify(this.conf)),n=[],l={},u=E[t.tag];u&&Object.keys(u).forEach(o=>{const r=u[o];t[o]&&n.push(r(h,t,o))});const m=P[t.tag];return m&&Object.keys(m).forEach(o=>{const r=m[o];t[o]&&(l[o]=r(h,t,o))}),Object.keys(t).forEach(o=>{const r=t[o];e[o]?e[o]=r:A(o)?e.attrs[o]=r:B(o)||(e.props[o]=r)}),n.length>0&&(l.default=()=>n),h(a(this.conf.tag),{modelValue:this.$attrs.modelValue,...e.props,...e.attrs,style:{...e.style}},l??null)},props:{conf:{type:Object,required:!0}}}),G={class:"component-name"},J=j({name:"DraggableItem"}),W=Object.assign(J,{props:{element:Object,index:Number,drawingList:Array,activeId:{type:[String,Number]},formConf:Object},emits:["activeItem","copyItem","deleteItem"],setup(e,{emit:t}){const n=e,l=k(""),u=k(null),m=t;function o(c){m("activeItem",c)}function r(c,i){m("copyItem",c,i??n.drawingList)}function w(c,i){m("deleteItem",c,i??n.drawingList)}function V(){return{gutter:n.element.gutter,justify:n.element.justify,align:n.element.align}}return T(()=>n.activeId,c=>{l.value=(n.element.layout==="rowFormItem"?"drawing-row-item":"drawing-item")+(c===n.element.formId?" active-from-item":""),n.formConf.unFocusedComponentBorder&&(l.value+=" unfocus-bordered")},{immediate:!0}),(c,i)=>{const D=a("el-form-item"),N=a("draggable-item",!0),$=a("el-row"),q=a("CopyDocument"),I=a("el-icon"),z=a("Delete"),S=a("el-col");return f(),p(S,{span:e.element.span,class:C(v(l)),onClick:i[4]||(i[4]=g(s=>o(e.element),["stop"]))},{default:d(()=>[e.element.layout==="colFormItem"?(f(),p(D,{key:0,label:e.element.label,"label-width":e.element.labelWidth?e.element.labelWidth+"px":null,required:e.element.required},{default:d(()=>[(f(),p(v(U),{key:e.element.tag,conf:e.element,modelValue:e.element.defaultValue,"onUpdate:modelValue":i[0]||(i[0]=s=>e.element.defaultValue=s)},null,8,["conf","modelValue"]))]),_:1},8,["label","label-width","required"])):(f(),p($,{key:1,gutter:e.element.gutter,class:C(e.element.class),onClick:i[1]||(i[1]=g(s=>o(e.element),["stop"]))},{default:d(()=>[y("span",G,F(e.element.componentName),1),b(v(L),{group:"componentsGroup",animation:340,list:e.element.children,class:"drag-wrapper","item-key":"label",ref_key:"draggableItemRef",ref:u,"component-data":V()},{item:d(s=>[(f(),p(N,{key:s.element.renderKey,"drawing-list":e.element.children,element:s.element,index:e.index,"active-id":e.activeId,"form-conf":e.formConf,onActiveItem:x=>o(s.element),onCopyItem:x=>r(s.element,e.element.children),onDeleteItem:x=>w(s.index,e.element.children)},null,8,["drawing-list","element","index","active-id","form-conf","onActiveItem","onCopyItem","onDeleteItem"]))]),_:1},8,["list","component-data"])]),_:1},8,["gutter","class"])),y("span",{class:"drawing-item-copy",title:"复制",onClick:i[2]||(i[2]=g(s=>r(e.element),["stop"]))},[b(I,null,{default:d(()=>[b(q)]),_:1})]),y("span",{class:"drawing-item-delete",title:"删除",onClick:i[3]||(i[3]=g(s=>w(e.index),["stop"]))},[b(I,null,{default:d(()=>[b(z)]),_:1})])]),_:1},8,["span","class"])}}});export{W as default};
