import{d as g}from"./user-CV6IIF7D.js";import{d as c,A as b,r as V,e as n,j as x,o as y,i as l,f as r,l as s,n as w}from"./index-CdiCNU81.js";const $={__name:"resetPwd",setup(q){const{proxy:u}=c(),o=b({oldPassword:void 0,newPassword:void 0,confirmPassword:void 0}),i=V({oldPassword:[{required:!0,message:"旧密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{required:!0,validator:(p,e,d)=>{o.newPassword!==e?d(new Error("两次输入的密码不一致")):d()},trigger:"blur"}]});function f(){u.$refs.pwdRef.validate(p=>{p&&g(o.oldPassword,o.newPassword).then(e=>{u.$modal.msgSuccess("修改成功")})})}function P(){u.$tab.closePage()}return(p,e)=>{const d=n("el-input"),t=n("el-form-item"),m=n("el-button"),_=n("el-form");return y(),x(_,{ref:"pwdRef",model:s(o),rules:s(i),"label-width":"80px"},{default:l(()=>[r(t,{label:"旧密码",prop:"oldPassword"},{default:l(()=>[r(d,{modelValue:s(o).oldPassword,"onUpdate:modelValue":e[0]||(e[0]=a=>s(o).oldPassword=a),placeholder:"请输入旧密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"新密码",prop:"newPassword"},{default:l(()=>[r(d,{modelValue:s(o).newPassword,"onUpdate:modelValue":e[1]||(e[1]=a=>s(o).newPassword=a),placeholder:"请输入新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),r(t,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[r(d,{modelValue:s(o).confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=a=>s(o).confirmPassword=a),placeholder:"请确认新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),r(t,null,{default:l(()=>[r(m,{type:"primary",onClick:f},{default:l(()=>e[3]||(e[3]=[w("保存")])),_:1,__:[3]}),r(m,{type:"danger",onClick:P},{default:l(()=>e[4]||(e[4]=[w("关闭")])),_:1,__:[4]})]),_:1})]),_:1},8,["model","rules"])}}};export{$ as default};
