import{_ as B,ac as D,r as c,ad as L,aC as O,w as z,e as d,c as u,o as t,f as r,i as m,h as p,J as M,K as N,l as f,L as S,j,a6 as E,t as U,n as F,C as J}from"./index-CdiCNU81.js";const K={class:"icon-dialog"},P={class:"icon-ul"},R=["onClick"],T={__name:"IconsDialog",props:{modelValue:{},modelModifiers:{}},emits:D(["select"],["update:modelValue"]),setup(v,{emit:V}){const s=c([]),n=[],a=c(""),_=c(""),C=V,i=L(v,"modelValue");for(const[e]of Object.entries(O))s.value.push(e),n.push(e);function g(){}function k(){}function x(e){_.value=e,C("select",e),i.value=!1}return z(a,e=>{e?s.value=n.filter(l=>l.indexOf(e)>-1):s.value=n}),(e,l)=>{const h=d("el-input"),y=d("el-icon"),b=d("el-dialog");return t(),u("div",K,[r(b,{modelValue:i.value,"onUpdate:modelValue":l[1]||(l[1]=o=>i.value=o),width:"980px","close-on-click-modal":!1,"modal-append-to-body":!1,onOpen:g,onClose:k},{header:m(({close:o,titleId:w,titleClass:$})=>[l[2]||(l[2]=F(" 选择图标 ")),r(h,{modelValue:f(a),"onUpdate:modelValue":l[0]||(l[0]=I=>J(a)?a.value=I:null),size:"small",style:{width:"260px"},placeholder:"请输入图标名称","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),default:m(()=>[p("ul",P,[(t(!0),u(M,null,N(f(s),o=>(t(),u("li",{key:o,class:S(f(_)===o?"active-item":""),onClick:w=>x(o)},[p("div",null,[r(y,{size:30},{default:m(()=>[(t(),j(E(o)))]),_:2},1024),p("div",null,U(o),1)])],10,R))),128))])]),_:1},8,["modelValue"])])}}},A=B(T,[["__scopeId","data-v-6b634abc"]]);export{A as default};
