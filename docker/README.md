# SDL Platform Docker 部署指南

## 概述

SDL Platform 采用宿主机构建 + Docker部署的方案：
- 在宿主机上编译前后端代码
- 使用两个Docker容器分别部署前端和后端
- 连接外部MySQL和Redis服务

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────┐
│ 前端容器(Nginx)  │    │ 后端容器(Java)   │    │  外部数据库(MySQL)  │
│     Port: 80    │────│    Port: 8080   │────│ *************:3306 │
└─────────────────┘    └─────────────────┘    └─────────────────────┘
                              │
                       ┌─────────────────────┐
                       │   外部缓存(Redis)   │
                       │ *************:6379 │
                       └─────────────────────┘
```

## 系统要求

### 宿主机环境
- **Java**: JDK 17+
- **Maven**: 3.6+
- **Node.js**: 16+
- **pnpm**: 自动安装（如果未安装）
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 外部服务
- **MySQL**: *************:3306 (可访问)
- **Redis**: *************:6379 (可访问)

### Docker镜像
- **eclipse-temurin:17-jre** (后端Java运行时)
- **nginx:alpine** (前端Web服务器)

## 快速开始

### 一键部署
```bash
# 给脚本执行权限
chmod +x *.sh

# 一键构建和部署
./start.sh

# 或强制重新构建
./start.sh --rebuild
```

### 分步部署

1. **构建应用**
   ```bash
   ./build.sh
   ```

2. **部署到Docker**
   ```bash
   ./deploy.sh
   ```

## 详细步骤

### 步骤1: 环境检查

```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查Node.js版本
node -v

# 检查pnpm版本（如果未安装会自动安装）
pnpm -v

# 检查Docker版本
docker --version
docker-compose --version
```

### 步骤2: 验证外部服务

```bash
# 测试MySQL连接
mysql -h ************* -P 3306 -u root -p

# 测试Redis连接
redis-cli -h ************* -p 6379 ping

# 或使用telnet测试端口
telnet ************* 3306
telnet ************* 6379
```

### 步骤3: 构建应用

```bash
# 运行构建脚本
./build.sh
```

构建过程包括：
- 后端Maven编译打包
- 前端pnpm构建（包含缓存清理）
- 复制构建产物到docker/deploy目录

### 步骤4: Docker部署

```bash
# 运行部署脚本
./deploy.sh
```

部署过程包括：
- 检查构建产物
- 构建Docker镜像
- 启动Docker容器
- 健康检查

## 文件结构

```
sdl-platform/
├── build.sh                    # 宿主机构建脚本
├── deploy.sh                   # Docker部署脚本
├── start.sh                    # 一键部署脚本
├── quick-update.sh             # 快速更新脚本（开发用）
├── dev-mode.sh                 # 开发模式脚本（热更新）
├── docker-compose.yml          # Docker编排文件
└── docker/
    ├── README.md               # 本文档
    ├── backend.Dockerfile      # 后端镜像构建文件
    ├── frontend.Dockerfile     # 前端镜像构建文件
    ├── nginx.conf              # Nginx配置文件
    └── deploy/                 # 构建产物目录(自动生成)
        ├── backend/
        │   └── sdl-platform-admin.jar
        └── frontend/
            ├── index.html
            └── ...
```

## 访问地址

- **前端**: http://localhost
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **Druid监控**: http://localhost:8080/druid

## 默认账号

- **用户名**: admin
- **密码**: admin123

## 常用命令

### 服务管理
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
docker-compose logs -f sdl-backend
docker-compose logs -f sdl-frontend

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新部署
./start.sh --rebuild
```

### 开发调试
```bash
# 只重新构建后端
mvn clean package -DskipTests
cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/
docker-compose restart sdl-backend

# 只重新构建前端
cd sdl-platform-vue3
pnpm run build:prod
cd ..
cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/
docker-compose restart sdl-frontend
```

## 开发过程中快速更新代码

在开发过程中，您可能需要频繁地将修改的代码更新到Docker容器中进行测试。以下是几种快速更新的方法：

### 方法一：使用快速更新脚本（推荐）

我们提供了一个便捷的更新脚本 `quick-update.sh`，可以快速更新指定的服务：

```bash
# 给脚本执行权限
chmod +x quick-update.sh

# 更新后端代码
./quick-update.sh backend

# 更新前端代码
./quick-update.sh frontend

# 同时更新前后端
./quick-update.sh all

# 强制重新构建并更新
./quick-update.sh backend --rebuild
./quick-update.sh frontend --rebuild
```

### 方法二：手动分步更新

#### 更新后端代码
```bash
# 1. 编译后端代码
mvn clean package -DskipTests

# 2. 复制jar文件到部署目录
cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/

# 3. 重启后端容器
docker-compose restart sdl-backend

# 4. 查看启动日志（可选）
docker-compose logs -f sdl-backend
```

#### 更新前端代码
```bash
# 1. 进入前端目录并构建
cd sdl-platform-vue3
pnpm run build:prod
cd ..

# 2. 复制构建产物到部署目录
cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/

# 3. 重启前端容器
docker-compose restart sdl-frontend

# 4. 查看启动日志（可选）
docker-compose logs -f sdl-frontend
```

#### 同时更新前后端
```bash
# 1. 构建后端
mvn clean package -DskipTests
cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/

# 2. 构建前端
cd sdl-platform-vue3
pnpm run build:prod
cd ..
cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/

# 3. 重启所有服务
docker-compose restart

# 4. 查看所有服务状态
docker-compose ps
```

### 方法三：热更新模式（开发环境）

如果您需要更频繁的代码更新，可以使用开发模式脚本启动热更新环境：

```bash
# 给脚本执行权限
chmod +x dev-mode.sh

# 启动前端开发服务器（支持热更新）
./dev-mode.sh frontend

# 启动后端开发服务器（支持热更新）
./dev-mode.sh backend

# 同时启动前后端开发服务器
./dev-mode.sh both

# 停止所有开发服务器
./dev-mode.sh stop

# 自定义端口启动前端
./dev-mode.sh frontend --port 3001

# 指定Spring配置文件启动后端
./dev-mode.sh backend --profile dev
```

#### 手动启动热更新模式
```bash
# 后端热更新
mvn spring-boot:run -Dspring-boot.run.profiles=dev -pl sdl-platform-admin

# 前端热更新
cd sdl-platform-vue3
pnpm run dev
# 前端将在 http://localhost:3000 运行，支持热更新
```

**开发模式优势：**
- 代码修改后自动重新加载，无需手动重启
- 前端支持HMR（热模块替换），页面状态保持
- 后端支持Spring Boot DevTools，Java代码修改后自动重启
- 独立于Docker环境，启动更快

### 更新验证

更新完成后，建议进行以下验证：

```bash
# 1. 检查容器状态
docker-compose ps

# 2. 检查服务健康状态
curl -f http://localhost:8080/actuator/health  # 后端健康检查
curl -f http://localhost/                      # 前端健康检查

# 3. 查看服务日志
docker-compose logs --tail=50 sdl-backend     # 查看后端最新50行日志
docker-compose logs --tail=50 sdl-frontend    # 查看前端最新50行日志

# 4. 访问应用
# 前端: http://localhost
# 后端API: http://localhost:8080
# API文档: http://localhost:8080/swagger-ui.html
```

### 常见更新问题及解决方案

#### 1. 后端更新后无法启动
```bash
# 检查jar文件是否正确生成
ls -la docker/deploy/backend/sdl-platform-admin.jar

# 查看详细错误日志
docker-compose logs sdl-backend

# 如果是依赖问题，重新完整构建
./build.sh
docker-compose restart sdl-backend
```

#### 2. 前端更新后页面空白或404
```bash
# 检查前端文件是否正确复制
ls -la docker/deploy/frontend/

# 查看nginx日志
docker-compose logs sdl-frontend

# 如果是构建问题，清理缓存重新构建
cd sdl-platform-vue3
rm -rf node_modules/.cache dist .vite
pnpm run build:prod
cd ..
cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/
docker-compose restart sdl-frontend
```

#### 3. 容器重启失败
```bash
# 查看容器状态
docker-compose ps

# 强制重新创建容器
docker-compose down
docker-compose up -d

# 如果还是失败，重新构建镜像
docker-compose build --no-cache
docker-compose up -d
```

### 性能优化建议

1. **增量构建**: 如果只修改了少量文件，可以考虑只构建修改的部分
2. **并行构建**: 前后端可以并行构建以节省时间
3. **缓存利用**: 充分利用Maven和pnpm的缓存机制
4. **开发环境**: 开发时使用热更新模式，减少Docker重启次数

## 快速参考

### 常用开发命令速查

```bash
# === 完整部署 ===
./start.sh                      # 一键构建和部署
./start.sh --rebuild            # 强制重新构建并部署

# === 快速更新（推荐开发使用） ===
./quick-update.sh backend       # 快速更新后端
./quick-update.sh frontend      # 快速更新前端
./quick-update.sh all           # 更新前后端
./quick-update.sh backend --rebuild --logs  # 强制重建并显示日志

# === 开发模式（热更新） ===
./dev-mode.sh frontend          # 启动前端开发服务器
./dev-mode.sh backend           # 启动后端开发服务器
./dev-mode.sh both              # 同时启动前后端
./dev-mode.sh stop              # 停止开发服务器

# === Docker服务管理 ===
docker-compose ps               # 查看服务状态
docker-compose logs -f          # 查看所有日志
docker-compose restart          # 重启所有服务
docker-compose down             # 停止所有服务

# === 手动构建 ===
mvn package -DskipTests         # 构建后端
cd sdl-platform-vue3 && pnpm run build:prod  # 构建前端
```

### 开发工作流推荐

1. **首次部署**：
   ```bash
   ./start.sh
   ```

2. **日常开发**（推荐使用热更新）：
   ```bash
   ./dev-mode.sh both              # 启动开发环境
   # 修改代码，自动热更新
   ./dev-mode.sh stop              # 完成开发后停止
   ```

3. **测试部署版本**：
   ```bash
   ./quick-update.sh all           # 快速更新到Docker
   ```

4. **生产部署**：
   ```bash
   ./start.sh --rebuild            # 完整重新构建部署
   ```

### 访问地址速查

| 服务 | 开发模式 | Docker模式 | 说明 |
|------|----------|------------|------|
| 前端 | http://localhost:3000 | http://localhost | 主应用界面 |
| 后端API | http://localhost:8080 | http://localhost:8080 | REST API |
| API文档 | http://localhost:8080/swagger-ui.html | http://localhost:8080/swagger-ui.html | Swagger文档 |
| 数据库监控 | http://localhost:8080/druid | http://localhost:8080/druid | Druid监控 |

### 故障排除速查

| 问题 | 解决方案 |
|------|----------|
| 构建失败 | `mvn clean` 然后重新构建 |
| 前端依赖问题 | `cd sdl-platform-vue3 && rm -rf node_modules && pnpm install` |
| Docker启动失败 | `docker-compose down && docker-compose up -d` |
| 端口被占用 | 检查并停止占用80/8080端口的进程 |
| 服务无响应 | `docker-compose logs <service-name>` 查看日志 |

## 故障排除

### 构建问题

1. **Maven构建失败**
   ```bash
   # 清理并重试
   mvn clean
   mvn package -DskipTests -X
   ```

2. **pnpm构建失败**
   ```bash
   # 清理缓存并重新安装
   cd sdl-platform-vue3
   rm -rf node_modules pnpm-lock.yaml
   pnpm store prune
   pnpm install
   pnpm run build:prod
   ```

3. **缺失依赖问题（如sortablejs）**
   ```bash
   # 手动安装缺失的依赖
   cd sdl-platform-vue3
   pnpm add sortablejs
   pnpm run build:prod
   ```

### 部署问题

1. **Docker镜像构建失败**
   ```bash
   # 查看详细构建日志
   docker-compose build --no-cache
   ```

2. **服务启动失败**
   ```bash
   # 查看容器日志
   docker-compose logs sdl-backend
   docker-compose logs sdl-frontend
   ```

3. **外部服务连接失败**
   ```bash
   # 检查网络连通性
   ping *************
   telnet ************* 3306
   telnet ************* 6379
   ```

### 清理和重置

```bash
# 完全清理
docker-compose down
docker rmi $(docker images | grep sdl | awk '{print $3}') 2>/dev/null || true
rm -rf docker/deploy
./start.sh --rebuild
```

## 注意事项

1. **构建环境**: 确保宿主机有完整的Java和Node.js开发环境
2. **外部服务**: 确保能够访问外部MySQL和Redis服务
3. **端口占用**: 确保80和8080端口未被占用
4. **Docker镜像**: 确保可以拉取eclipse-temurin:17-jre和nginx:alpine镜像

---

如有问题，请检查日志并参考故障排除部分。
