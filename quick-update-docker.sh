#!/bin/bash

# SDL Platform Docker 快速更新脚本
# 用于快速更新修改的代码到Docker容器中

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================="
echo "    SDL Platform Docker 快速更新"
echo "========================================="

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    if ! docker ps --format "table {{.Names}}" | grep -E "sdl-(backend|frontend)" &> /dev/null; then
        log_error "SDL Platform 服务未运行，请先运行 ./deploy-docker.sh"
        exit 1
    fi
    
    log_success "服务状态检查通过"
}

# 更新后端
update_backend() {
    log_info "更新后端服务..."
    
    # 重新构建后端镜像
    log_info "重新构建后端镜像..."
    if $COMPOSE_CMD build --no-cache sdl-backend; then
        log_success "后端镜像构建成功"
    else
        log_error "后端镜像构建失败"
        return 1
    fi
    
    # 重启后端服务
    log_info "重启后端服务..."
    if $COMPOSE_CMD up -d --no-deps sdl-backend; then
        log_success "后端服务重启成功"
    else
        log_error "后端服务重启失败"
        return 1
    fi
    
    # 等待服务启动
    log_info "等待后端服务启动..."
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "后端服务启动成功"
            return 0
        else
            log_info "等待后端服务启动... ($attempt/$max_attempts)"
            sleep 3
            ((attempt++))
        fi
    done
    
    log_error "后端服务启动超时"
    docker logs sdl-backend --tail 20
    return 1
}

# 更新前端
update_frontend() {
    log_info "更新前端服务..."
    
    # 重新构建前端镜像
    log_info "重新构建前端镜像..."
    if $COMPOSE_CMD build --no-cache sdl-frontend; then
        log_success "前端镜像构建成功"
    else
        log_error "前端镜像构建失败"
        return 1
    fi
    
    # 重启前端服务
    log_info "重启前端服务..."
    if $COMPOSE_CMD up -d --no-deps sdl-frontend; then
        log_success "前端服务重启成功"
    else
        log_error "前端服务重启失败"
        return 1
    fi
    
    # 检查前端服务
    sleep 5
    if curl -f http://localhost/ &> /dev/null; then
        log_success "前端服务检查通过"
    else
        log_warning "前端服务检查失败，但可能是正常的"
    fi
    
    return 0
}

# 更新所有服务
update_all() {
    log_info "更新所有服务..."
    
    # 重新构建所有镜像
    log_info "重新构建所有镜像..."
    if $COMPOSE_CMD build --no-cache; then
        log_success "所有镜像构建成功"
    else
        log_error "镜像构建失败"
        return 1
    fi
    
    # 重启所有服务
    log_info "重启所有服务..."
    if $COMPOSE_CMD up -d; then
        log_success "所有服务重启成功"
    else
        log_error "服务重启失败"
        return 1
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查后端服务
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "后端服务启动成功"
            break
        else
            log_info "等待后端服务启动... ($attempt/$max_attempts)"
            sleep 3
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "后端服务启动超时"
        docker logs sdl-backend --tail 20
        return 1
    fi
    
    # 检查前端服务
    if curl -f http://localhost/ &> /dev/null; then
        log_success "前端服务检查通过"
    else
        log_warning "前端服务检查失败，但可能是正常的"
    fi
    
    return 0
}

# 显示服务状态
show_status() {
    echo ""
    log_info "当前服务状态："
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(NAMES|sdl-)"
    echo ""
    
    log_info "服务访问地址："
    echo "  前端: http://localhost"
    echo "  后端: http://localhost:8080"
    echo "  后端健康检查: http://localhost:8080/actuator/health"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  backend, -b      仅更新后端服务"
    echo "  frontend, -f     仅更新前端服务"
    echo "  all, -a          更新所有服务（默认）"
    echo "  status, -s       显示服务状态"
    echo "  logs, -l         显示服务日志"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 backend       # 仅更新后端"
    echo "  $0 frontend      # 仅更新前端"
    echo "  $0 all           # 更新所有服务"
    echo "  $0 status        # 显示状态"
    echo ""
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    $COMPOSE_CMD logs -f --tail=50
}

# 主函数
main() {
    local action="all"
    
    # 解析命令行参数
    case "${1:-all}" in
        backend|-b)
            action="backend"
            ;;
        frontend|-f)
            action="frontend"
            ;;
        all|-a)
            action="all"
            ;;
        status|-s)
            action="status"
            ;;
        logs|-l)
            action="logs"
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
    
    check_docker
    
    if [ "$action" = "status" ]; then
        check_services
        show_status
        exit 0
    fi
    
    if [ "$action" = "logs" ]; then
        check_services
        show_logs
        exit 0
    fi
    
    check_services
    
    case $action in
        backend)
            if update_backend; then
                log_success "后端更新完成！"
                show_status
            else
                log_error "后端更新失败！"
                exit 1
            fi
            ;;
        frontend)
            if update_frontend; then
                log_success "前端更新完成！"
                show_status
            else
                log_error "前端更新失败！"
                exit 1
            fi
            ;;
        all)
            if update_all; then
                log_success "所有服务更新完成！"
                show_status
            else
                log_error "服务更新失败！"
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
