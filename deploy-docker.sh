#!/bin/bash

# SDL Platform Docker 一键部署脚本
# 在任意主机上通过容器化构建和部署SDL Platform

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================="
echo "    SDL Platform Docker 一键部署"
echo "========================================="

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装Docker"
        log_error "安装指南: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker 版本: $(docker --version)"
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装Docker Compose"
        exit 1
    fi
    
    if docker compose version &> /dev/null; then
        log_success "Docker Compose 版本: $(docker compose version)"
        COMPOSE_CMD="docker compose"
    else
        log_success "Docker Compose 版本: $(docker-compose --version)"
        COMPOSE_CMD="docker-compose"
    fi
}

# 预拉取基础镜像
pull_base_images() {
    log_info "预拉取基础镜像..."
    
    local images=(
        "maven:3.9-eclipse-temurin-17"
        "node:18-alpine"
        "eclipse-temurin:17-jre"
        "nginx:alpine"
    )
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        if docker pull "$image"; then
            log_success "镜像拉取成功: $image"
        else
            log_error "镜像拉取失败: $image"
            exit 1
        fi
    done
}

# 清理旧的容器和镜像
cleanup_old() {
    log_info "清理旧的容器和镜像..."
    
    # 停止并删除旧容器
    if docker ps -a --format "table {{.Names}}" | grep -E "sdl-(backend|frontend)" &> /dev/null; then
        log_info "停止旧容器..."
        $COMPOSE_CMD down --remove-orphans || true
    fi
    
    # 删除旧的应用镜像（保留基础镜像）
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "sdl-platform" &> /dev/null; then
        log_info "删除旧的应用镜像..."
        docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "sdl-platform" | awk '{print $1}' | xargs -r docker rmi -f || true
    fi
    
    # 清理构建缓存
    log_info "清理Docker构建缓存..."
    docker builder prune -f || true
    
    log_success "清理完成"
}

# 构建和启动服务
build_and_start() {
    log_info "开始构建和启动服务..."
    
    # 构建镜像
    log_info "构建Docker镜像..."
    if $COMPOSE_CMD build --no-cache; then
        log_success "镜像构建成功"
    else
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 启动服务
    log_info "启动服务..."
    if $COMPOSE_CMD up -d; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查容器状态
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "sdl-(backend|frontend)"; then
        log_success "容器状态检查完成"
    else
        log_error "容器状态异常"
        $COMPOSE_CMD logs
        exit 1
    fi
    
    # 检查后端健康状态
    log_info "检查后端服务健康状态..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "后端服务健康检查通过"
            break
        else
            log_info "等待后端服务启动... ($attempt/$max_attempts)"
            sleep 5
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "后端服务健康检查失败"
        log_error "查看后端日志:"
        docker logs sdl-backend --tail 50
        exit 1
    fi
    
    # 检查前端服务
    log_info "检查前端服务..."
    if curl -f http://localhost/ &> /dev/null; then
        log_success "前端服务检查通过"
    else
        log_warning "前端服务检查失败，但可能是正常的"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "部署完成！"
    echo ""
    echo "服务访问地址："
    echo "  前端: http://localhost"
    echo "  后端: http://localhost:8080"
    echo "  后端健康检查: http://localhost:8080/actuator/health"
    echo ""
    echo "常用命令："
    echo "  查看服务状态: $COMPOSE_CMD ps"
    echo "  查看日志: $COMPOSE_CMD logs -f"
    echo "  停止服务: $COMPOSE_CMD down"
    echo "  重启服务: $COMPOSE_CMD restart"
    echo ""
    echo "数据卷："
    echo "  上传文件: backend_uploads"
    echo "  应用日志: backend_logs"
    echo ""
}

# 主函数
main() {
    local skip_pull=false
    local skip_cleanup=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-pull)
                skip_pull=true
                shift
                ;;
            --skip-cleanup)
                skip_cleanup=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-pull      跳过基础镜像拉取"
                echo "  --skip-cleanup   跳过清理步骤"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    check_docker
    
    if [ "$skip_pull" = false ]; then
        pull_base_images
    fi
    
    if [ "$skip_cleanup" = false ]; then
        cleanup_old
    fi
    
    build_and_start
    check_services
    show_deployment_info
}

# 执行主函数
main "$@"
