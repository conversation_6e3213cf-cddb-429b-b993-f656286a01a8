services:
  # 后端服务
  sdl-backend:
    build:
      context: docker
      dockerfile: backend.Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs

  # 前端服务
  sdl-frontend:
    build:
      context: docker
      dockerfile: frontend.Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - sdl-backend

# 数据卷配置
volumes:
  backend_uploads:
    driver: local
  backend_logs:
    driver: local