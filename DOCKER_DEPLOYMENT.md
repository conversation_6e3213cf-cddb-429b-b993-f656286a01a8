# SDL Platform Docker 一键部署指南

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装Docker：

```bash
# 检查Docker版本
docker --version
docker compose version
```

如果未安装Docker，请参考：[Docker官方安装指南](https://docs.docker.com/get-docker/)

### 2. 一键部署

```bash
# 进入项目目录
cd sdl-platform

# 执行一键部署
./deploy-docker.sh
```

部署过程包括：
- ✅ 检查Docker环境
- ✅ 拉取基础镜像（Maven、Node.js、JRE、Nginx）
- ✅ 清理旧容器和镜像
- ✅ 多阶段构建新镜像
- ✅ 启动服务容器
- ✅ 健康检查

### 3. 访问应用

部署完成后访问：
- **前端应用**: http://localhost
- **后端API**: http://localhost:8080
- **健康检查**: http://localhost:8080/actuator/health

## 🔄 开发工作流

### 代码修改后快速更新

```bash
# 更新所有服务（推荐）
./quick-update-docker.sh

# 仅更新后端
./quick-update-docker.sh backend

# 仅更新前端
./quick-update-docker.sh frontend
```

### 查看服务状态

```bash
# 查看服务状态
./quick-update-docker.sh status

# 查看实时日志
./quick-update-docker.sh logs
```

## 📋 常用命令

### Docker Compose 命令

```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 完全清理（包括数据卷）
docker compose down -v
```

### 容器管理

```bash
# 进入后端容器
docker exec -it sdl-backend bash

# 进入前端容器
docker exec -it sdl-frontend sh

# 查看容器资源使用
docker stats
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口冲突
如果80或8080端口被占用：

```bash
# 查看端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8080

# 停止占用端口的服务或修改docker-compose.yml中的端口映射
```

#### 2. 构建失败
```bash
# 清理Docker缓存
docker builder prune -f

# 重新部署
./deploy-docker.sh
```

#### 3. 服务启动失败
```bash
# 查看详细日志
docker logs sdl-backend --tail 100
docker logs sdl-frontend --tail 100
```

#### 4. 内存不足
修改 `docker-compose.yml` 中的内存设置：

```yaml
environment:
  JAVA_OPTS: "-Xms256m -Xmx512m"
```

## 🏗️ 技术架构

### 多阶段构建

#### 后端构建流程
```
源代码 → Maven构建容器 → 编译打包 → JRE运行容器 → 后端服务
```

#### 前端构建流程
```
源代码 → Node.js构建容器 → 编译打包 → Nginx运行容器 → 前端服务
```

### 镜像说明

- **maven:3.9-eclipse-temurin-17**: 后端编译环境
- **node:18-alpine**: 前端编译环境  
- **eclipse-temurin:17-jre**: 后端运行环境
- **nginx:alpine**: 前端运行环境

### 网络架构

```
Internet → Nginx(80) → 前端静态文件
         ↓
         Backend(8080) → 后端API服务
```

## 📊 性能优化

### 构建优化
- 利用Docker层缓存
- 分离依赖下载和代码编译
- 使用国内镜像源加速

### 运行优化
- 合理配置JVM内存参数
- 启用健康检查
- 配置重启策略

## 🔒 安全建议

- 定期更新基础镜像
- 不在镜像中包含敏感信息
- 配置防火墙规则
- 使用非root用户运行（可选）

## 📝 部署选项

### 跳过镜像拉取（适用于重复部署）
```bash
./deploy-docker.sh --skip-pull
```

### 跳过清理步骤（保留现有数据）
```bash
./deploy-docker.sh --skip-cleanup
```

### 组合使用
```bash
./deploy-docker.sh --skip-pull --skip-cleanup
```

## 🎯 最佳实践

1. **首次部署**: 使用完整的 `./deploy-docker.sh`
2. **日常开发**: 使用 `./quick-update-docker.sh`
3. **问题排查**: 使用 `./quick-update-docker.sh logs`
4. **定期维护**: 定期清理Docker缓存和未使用镜像

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 `docker/README.md` 获取详细信息
3. 查看容器日志定位问题
4. 联系技术支持团队

---

**注意**: 此部署方案适用于开发和测试环境。生产环境部署请参考相应的生产部署指南。
