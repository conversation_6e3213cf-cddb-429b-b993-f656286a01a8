#!/bin/bash

# SDL Platform 一键部署脚本
# 自动构建和部署

set -e

echo "========================================="
echo "    SDL Platform 一键部署"
echo "    (宿主机构建 + <PERSON><PERSON>部署)"
echo "========================================="

# 检查是否需要重新构建
need_rebuild=false

if [ "$1" = "--rebuild" ] || [ "$1" = "-r" ]; then
    need_rebuild=true
    echo "🔄 强制重新构建模式"
elif [ ! -f "docker/deploy/backend/sdl-platform-admin.jar" ] || [ ! -d "docker/deploy/frontend" ]; then
    need_rebuild=true
    echo "📦 检测到缺少构建产物，将自动构建"
else
    echo "✅ 发现已有构建产物"
    read -p "是否重新构建? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        need_rebuild=true
    fi
fi

# 执行构建
if [ "$need_rebuild" = true ]; then
    echo "🔨 开始构建..."
    ./build.sh
else
    echo "⏭️  跳过构建，使用现有产物"
fi

# 执行部署
echo "🚀 开始部署..."
./deploy.sh

echo ""
echo "🎉 一键部署完成！"
