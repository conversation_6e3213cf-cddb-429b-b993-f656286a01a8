# SDL Platform 项目结构

## 根目录文件

### 部署脚本
- `deploy-docker.sh` - Docker一键部署脚本
- `quick-update-docker.sh` - 快速更新脚本
- `build.sh` - 传统构建脚本（需要本地环境）

### 配置文件
- `docker-compose.yml` - Docker编排配置
- `pom.xml` - Maven主配置文件
- `.dockerignore` - Docker构建忽略文件
- `.gitignore` - Git忽略文件

## 目录结构

```
sdl-platform/
├── docs/                          # 📚 文档目录
│   ├── 快速开始.md                 # 快速开始指南
│   ├── Docker部署指南.md           # Docker部署详细说明
│   ├── OpenAPI使用指南.md          # API接口文档
│   └── 项目结构.md                 # 本文档
│
├── docker/                        # 🐳 Docker相关文件
│   ├── backend.Dockerfile         # 后端镜像构建文件
│   ├── frontend.Dockerfile        # 前端镜像构建文件
│   └── nginx.conf                 # Nginx配置文件
│
├── sdl-platform-admin/            # 🎯 主应用模块
│   ├── src/                       # 源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-framework/        # 🏗️ 框架核心模块
│   ├── src/                       # 框架源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-system/           # 👥 系统管理模块
│   ├── src/                       # 系统管理源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-common/           # 🔧 通用工具模块
│   ├── src/                       # 通用工具源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-business/         # 💼 业务模块
│   ├── src/                       # 业务源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-quartz/           # ⏰ 定时任务模块
│   ├── src/                       # 定时任务源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-generator/        # 🔄 代码生成模块
│   ├── src/                       # 代码生成源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-vue3/             # 🎨 前端Vue3项目
│   ├── src/                       # Vue3源代码
│   ├── public/                    # 静态资源
│   ├── package.json               # npm配置
│   ├── vite.config.js             # Vite配置
│   └── dist/                      # 构建产物（忽略）
│
├── sql/                           # 🗄️ 数据库脚本
│   ├── ry_20250522.sql            # 主数据库脚本
│   ├── quartz.sql                 # 定时任务表
│   ├── openapi_tables.sql         # OpenAPI表结构
│   └── openapi_menu.sql           # OpenAPI菜单数据
│
└── bin/                           # 🪟 Windows批处理脚本
    ├── clean.bat                  # 清理脚本
    ├── package.bat                # 打包脚本
    └── run.bat                    # 运行脚本
```

## 模块说明

### 后端模块

| 模块 | 说明 | 主要功能 |
|------|------|----------|
| `sdl-platform-admin` | 主应用模块 | 启动类、主要配置 |
| `sdl-platform-framework` | 框架核心 | 安全、缓存、配置等 |
| `sdl-platform-system` | 系统管理 | 用户、角色、权限管理 |
| `sdl-platform-common` | 通用工具 | 工具类、常量、异常等 |
| `sdl-platform-business` | 业务模块 | 具体业务逻辑 |
| `sdl-platform-quartz` | 定时任务 | 任务调度管理 |
| `sdl-platform-generator` | 代码生成 | 自动生成CRUD代码 |

### 前端模块

| 目录 | 说明 |
|------|------|
| `src/` | Vue3源代码 |
| `src/views/` | 页面组件 |
| `src/components/` | 通用组件 |
| `src/api/` | API接口定义 |
| `src/utils/` | 工具函数 |
| `src/router/` | 路由配置 |
| `src/store/` | 状态管理 |

## 构建产物

### Docker构建
- 后端：`sdl-platform-admin/target/sdl-platform-admin.jar`
- 前端：`sdl-platform-vue3/dist/`

### 数据持久化
- `backend_uploads` - 文件上传存储
- `backend_logs` - 应用日志存储

## 开发建议

1. **后端开发**：修改Java代码后运行 `./quick-update-docker.sh backend`
2. **前端开发**：修改Vue代码后运行 `./quick-update-docker.sh frontend`
3. **全量更新**：运行 `./quick-update-docker.sh` 更新所有服务
4. **查看日志**：运行 `./quick-update-docker.sh logs` 查看实时日志
