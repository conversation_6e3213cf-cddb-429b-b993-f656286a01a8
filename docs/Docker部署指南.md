# SDL Platform Docker 部署指南

## 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 内存

### 一键部署
```bash
# 进入项目目录
cd sdl-platform

# 执行一键部署
./deploy-docker.sh
```

### 访问应用
- **前端**: http://localhost
- **后端**: http://localhost:8080
- **健康检查**: http://localhost:8080/actuator/health

## 开发工作流

### 代码修改后更新
```bash
# 更新所有服务
./quick-update-docker.sh

# 仅更新后端
./quick-update-docker.sh backend

# 仅更新前端
./quick-update-docker.sh frontend
```

### 查看状态和日志
```bash
# 查看服务状态
./quick-update-docker.sh status

# 查看实时日志
./quick-update-docker.sh logs
```

## 常用命令

### Docker Compose
```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down
```

### 容器管理
```bash
# 进入容器
docker exec -it sdl-backend bash
docker exec -it sdl-frontend sh

# 查看资源使用
docker stats
```

## 故障排除

### 端口冲突
如果80或8080端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8081:8080"  # 后端改为8081
  - "8082:80"    # 前端改为8082
```

### 构建失败
```bash
# 清理缓存重试
docker builder prune -f
./deploy-docker.sh
```

### 服务启动失败
```bash
# 查看详细日志
docker logs sdl-backend --tail 50
docker logs sdl-frontend --tail 50
```

## 技术架构

### 多阶段构建
- **后端**: Maven容器编译 → JRE容器运行
- **前端**: Node.js容器构建 → Nginx容器运行

### 使用的镜像
- `maven:3.9-eclipse-temurin-17` - 后端编译
- `node:18-alpine` - 前端编译
- `eclipse-temurin:17-jre` - 后端运行
- `nginx:alpine` - 前端运行

## 数据持久化

项目使用Docker数据卷持久化重要数据：
- `backend_uploads` - 上传文件存储
- `backend_logs` - 应用日志

```bash
# 查看数据卷
docker volume ls

# 备份数据
docker run --rm -v backend_uploads:/data -v $(pwd):/backup alpine tar czf /backup/uploads.tar.gz -C /data .
```

## 维护

### 定期清理
```bash
# 清理未使用的镜像
docker image prune -f

# 清理构建缓存
docker builder prune -f
```

### 版本升级
1. 更新代码
2. 运行 `./quick-update-docker.sh`
3. 验证服务正常
