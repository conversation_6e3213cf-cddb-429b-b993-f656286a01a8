# SDL Platform 常见问题

## 部署相关

### Q: Docker部署失败怎么办？
**A:** 按以下步骤排查：
1. 检查Docker是否正常运行：`docker --version`
2. 清理构建缓存：`docker builder prune -f`
3. 重新部署：`./deploy-docker.sh`
4. 查看详细错误：`docker logs 容器名`

### Q: 端口被占用怎么办？
**A:** 修改 `docker-compose.yml` 文件中的端口映射：
```yaml
services:
  sdl-backend:
    ports:
      - "8081:8080"  # 将8080改为8081
  sdl-frontend:
    ports:
      - "8082:80"    # 将80改为8082
```

### Q: 如何完全重新部署？
**A:** 执行以下命令：
```bash
# 停止并删除所有容器和数据卷
docker compose down -v

# 清理镜像
docker image prune -f

# 重新部署
./deploy-docker.sh
```

## 开发相关

### Q: 修改代码后如何快速更新？
**A:** 使用快速更新脚本：
```bash
# 更新所有服务
./quick-update-docker.sh

# 仅更新后端
./quick-update-docker.sh backend

# 仅更新前端
./quick-update-docker.sh frontend
```

### Q: 如何查看应用日志？
**A:** 多种方式查看日志：
```bash
# 实时日志
./quick-update-docker.sh logs

# 查看特定容器日志
docker logs sdl-backend -f
docker logs sdl-frontend -f

# 查看最近50行日志
docker logs sdl-backend --tail 50
```

### Q: 如何进入容器调试？
**A:** 使用以下命令进入容器：
```bash
# 进入后端容器
docker exec -it sdl-backend bash

# 进入前端容器
docker exec -it sdl-frontend sh
```

## 性能相关

### Q: 应用启动很慢怎么办？
**A:** 可能的优化方案：
1. 增加JVM内存：修改 `docker-compose.yml` 中的 `JAVA_OPTS`
2. 使用SSD硬盘
3. 增加系统内存
4. 检查网络连接（数据库、Redis等）

### Q: 如何监控资源使用？
**A:** 使用以下命令：
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop 或 top

# 查看磁盘使用
df -h
```

## 数据相关

### Q: 如何备份数据？
**A:** 备份Docker数据卷：
```bash
# 备份上传文件
docker run --rm -v backend_uploads:/data -v $(pwd):/backup alpine tar czf /backup/uploads.tar.gz -C /data .

# 备份日志文件
docker run --rm -v backend_logs:/data -v $(pwd):/backup alpine tar czf /backup/logs.tar.gz -C /data .
```

### Q: 如何恢复数据？
**A:** 恢复Docker数据卷：
```bash
# 恢复上传文件
docker run --rm -v backend_uploads:/data -v $(pwd):/backup alpine tar xzf /backup/uploads.tar.gz -C /data

# 恢复日志文件
docker run --rm -v backend_logs:/data -v $(pwd):/backup alpine tar xzf /backup/logs.tar.gz -C /data
```

### Q: 数据库连接失败怎么办？
**A:** 检查以下配置：
1. 数据库服务是否正常运行
2. 网络连接是否正常
3. 数据库配置是否正确
4. 防火墙设置是否阻止连接

## 网络相关

### Q: 前端无法访问后端API怎么办？
**A:** 检查以下几点：
1. 后端服务是否正常启动
2. 端口映射是否正确
3. 防火墙是否开放相应端口
4. 网络配置是否正确

### Q: 如何修改API地址？
**A:** 修改前端配置文件中的API地址，然后重新构建前端：
```bash
./quick-update-docker.sh frontend
```

## 其他问题

### Q: 如何查看服务状态？
**A:** 使用以下命令：
```bash
# 查看容器状态
./quick-update-docker.sh status

# 或者使用Docker命令
docker compose ps
```

### Q: 如何停止服务？
**A:** 停止服务：
```bash
# 停止服务但保留数据
docker compose down

# 停止服务并删除数据卷
docker compose down -v
```

### Q: 遇到其他问题怎么办？
**A:** 
1. 查看相关文档：[快速开始](快速开始.md)、[Docker部署指南](Docker部署指南.md)
2. 查看应用日志定位问题
3. 检查Docker和系统资源
4. 联系技术支持团队
