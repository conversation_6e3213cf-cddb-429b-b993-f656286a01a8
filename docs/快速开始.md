# SDL Platform 快速开始

## 部署方式选择

### Docker 部署（推荐）
适用于任意主机，无需安装开发环境：
```bash
./deploy-docker.sh
```

### 传统部署
需要宿主机安装Java、Maven、Node.js：
```bash
./build.sh
./deploy.sh
```

## Docker 部署详细步骤

### 1. 环境检查
确保已安装Docker：
```bash
docker --version
docker compose version
```

### 2. 一键部署
```bash
# 完整部署
./deploy-docker.sh

# 跳过镜像拉取（重复部署时）
./deploy-docker.sh --skip-pull
```

### 3. 验证部署
- 前端：http://localhost
- 后端：http://localhost:8080
- 健康检查：http://localhost:8080/actuator/health

## 开发工作流

### 代码修改后更新
```bash
# 快速更新所有服务
./quick-update-docker.sh

# 仅更新特定服务
./quick-update-docker.sh backend   # 仅后端
./quick-update-docker.sh frontend  # 仅前端
```

### 查看运行状态
```bash
# 服务状态
./quick-update-docker.sh status

# 实时日志
./quick-update-docker.sh logs
```

## 常见问题

### Q: 端口被占用怎么办？
A: 修改 `docker-compose.yml` 中的端口映射，如将80改为8080

### Q: 构建失败怎么办？
A: 运行 `docker builder prune -f` 清理缓存后重试

### Q: 如何查看详细错误？
A: 使用 `docker logs 容器名` 查看具体错误信息

### Q: 如何完全重新部署？
A: 运行 `docker compose down -v` 清理后重新执行 `./deploy-docker.sh`

## 项目结构

```
sdl-platform/
├── deploy-docker.sh          # Docker一键部署脚本
├── quick-update-docker.sh    # 快速更新脚本
├── docker-compose.yml        # Docker编排配置
├── docker/                   # Docker相关文件
│   ├── backend.Dockerfile    # 后端镜像构建
│   ├── frontend.Dockerfile   # 前端镜像构建
│   └── nginx.conf           # Nginx配置
├── docs/                    # 文档目录
│   ├── 快速开始.md
│   └── Docker部署指南.md
└── ...                      # 其他项目文件
```

## 下一步

- 详细部署说明：[Docker部署指南](Docker部署指南.md)
- API使用说明：[OpenAPI使用指南](OpenAPI使用指南.md)
