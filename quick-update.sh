#!/bin/bash

# SDL Platform 快速更新脚本
# 用于开发过程中快速更新代码到Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "SDL Platform 快速更新脚本"
    echo ""
    echo "用法: $0 <target> [options]"
    echo ""
    echo "目标 (target):"
    echo "  backend    - 只更新后端代码"
    echo "  frontend   - 只更新前端代码"
    echo "  all        - 更新前后端代码"
    echo ""
    echo "选项 (options):"
    echo "  --rebuild  - 强制重新构建（清理缓存）"
    echo "  --no-restart - 不重启容器（仅更新文件）"
    echo "  --logs     - 更新后显示服务日志"
    echo "  --help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 backend              # 快速更新后端"
    echo "  $0 frontend --rebuild   # 强制重新构建前端"
    echo "  $0 all --logs          # 更新全部并显示日志"
    echo ""
}

# 检查Docker服务状态
check_docker_status() {
    if ! docker-compose ps | grep -q "Up"; then
        log_warning "Docker服务未运行，正在启动..."
        docker-compose up -d
        sleep 5
    fi
}

# 更新后端
update_backend() {
    local rebuild=$1
    local no_restart=$2
    
    log_info "开始更新后端代码..."
    
    # 检查是否需要重新构建
    if [ "$rebuild" = true ]; then
        log_info "强制重新构建后端..."
        mvn clean package -DskipTests
    else
        # 检查是否有源码变更
        if [ -f "docker/deploy/backend/sdl-platform-admin.jar" ]; then
            jar_time=$(stat -c %Y docker/deploy/backend/sdl-platform-admin.jar 2>/dev/null || echo 0)
            src_time=$(find sdl-platform-admin/src -name "*.java" -newer docker/deploy/backend/sdl-platform-admin.jar | wc -l)
            
            if [ "$src_time" -gt 0 ]; then
                log_info "检测到源码变更，重新编译..."
                mvn package -DskipTests -pl sdl-platform-admin -am
            else
                log_info "源码无变更，使用现有jar文件..."
            fi
        else
            log_info "jar文件不存在，重新编译..."
            mvn package -DskipTests -pl sdl-platform-admin -am
        fi
    fi
    
    # 检查jar文件是否存在
    if [ ! -f "sdl-platform-admin/target/sdl-platform-admin.jar" ]; then
        log_error "后端jar文件未找到，构建可能失败"
        exit 1
    fi
    
    # 创建部署目录
    mkdir -p docker/deploy/backend
    
    # 复制jar文件
    cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/
    log_success "后端文件更新完成"
    
    # 重启容器
    if [ "$no_restart" != true ]; then
        log_info "重启后端容器..."
        docker-compose restart sdl-backend
        
        # 等待服务启动
        log_info "等待后端服务启动..."
        sleep 10
        
        # 健康检查
        for i in {1..30}; do
            if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
                log_success "后端服务启动成功"
                break
            fi
            if [ $i -eq 30 ]; then
                log_warning "后端服务启动超时，请检查日志"
            fi
            sleep 2
        done
    fi
}

# 更新前端
update_frontend() {
    local rebuild=$1
    local no_restart=$2
    
    log_info "开始更新前端代码..."
    
    cd sdl-platform-vue3
    
    # 检查是否需要重新构建
    if [ "$rebuild" = true ]; then
        log_info "强制重新构建前端..."
        rm -rf node_modules/.cache dist .vite 2>/dev/null || true
        pnpm store prune 2>/dev/null || true
        pnpm install
    else
        # 检查是否有源码变更
        if [ -d "../docker/deploy/frontend" ] && [ "$(ls -A ../docker/deploy/frontend 2>/dev/null)" ]; then
            src_time=$(find src -name "*.vue" -o -name "*.js" -o -name "*.ts" -newer ../docker/deploy/frontend/index.html 2>/dev/null | wc -l)
            
            if [ "$src_time" -gt 0 ]; then
                log_info "检测到源码变更，重新构建..."
            else
                log_info "源码无变更，跳过构建..."
                cd ..
                return
            fi
        else
            log_info "前端构建产物不存在，重新构建..."
        fi
    fi
    
    # 构建前端
    if pnpm run build:prod; then
        log_success "前端构建成功"
    else
        log_error "前端构建失败"
        cd ..
        exit 1
    fi
    
    cd ..
    
    # 创建部署目录
    mkdir -p docker/deploy/frontend
    
    # 复制构建产物
    cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/
    log_success "前端文件更新完成"
    
    # 重启容器
    if [ "$no_restart" != true ]; then
        log_info "重启前端容器..."
        docker-compose restart sdl-frontend
        
        # 等待服务启动
        log_info "等待前端服务启动..."
        sleep 5
        
        # 健康检查
        for i in {1..15}; do
            if curl -f http://localhost/ >/dev/null 2>&1; then
                log_success "前端服务启动成功"
                break
            fi
            if [ $i -eq 15 ]; then
                log_warning "前端服务启动超时，请检查日志"
            fi
            sleep 2
        done
    fi
}

# 显示服务日志
show_logs() {
    local target=$1
    
    log_info "显示服务日志..."
    
    case $target in
        "backend")
            docker-compose logs --tail=50 -f sdl-backend
            ;;
        "frontend")
            docker-compose logs --tail=50 -f sdl-frontend
            ;;
        "all")
            docker-compose logs --tail=50 -f
            ;;
    esac
}

# 主函数
main() {
    local target=""
    local rebuild=false
    local no_restart=false
    local show_logs_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            backend|frontend|all)
                target="$1"
                shift
                ;;
            --rebuild)
                rebuild=true
                shift
                ;;
            --no-restart)
                no_restart=true
                shift
                ;;
            --logs)
                show_logs_flag=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查目标参数
    if [ -z "$target" ]; then
        log_error "请指定更新目标: backend, frontend, 或 all"
        show_help
        exit 1
    fi
    
    echo "========================================="
    echo "    SDL Platform 快速更新"
    echo "========================================="
    echo "目标: $target"
    echo "重新构建: $rebuild"
    echo "跳过重启: $no_restart"
    echo "显示日志: $show_logs_flag"
    echo "========================================="
    
    # 检查Docker状态
    check_docker_status
    
    # 执行更新
    case $target in
        "backend")
            update_backend $rebuild $no_restart
            ;;
        "frontend")
            update_frontend $rebuild $no_restart
            ;;
        "all")
            update_backend $rebuild $no_restart
            update_frontend $rebuild $no_restart
            ;;
    esac
    
    # 显示服务状态
    echo ""
    log_info "服务状态:"
    docker-compose ps
    
    # 显示访问地址
    echo ""
    log_success "更新完成！"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost"
    echo "  后端API: http://localhost:8080"
    echo "  API文档: http://localhost:8080/swagger-ui.html"
    echo ""
    
    # 显示日志
    if [ "$show_logs_flag" = true ]; then
        show_logs $target
    fi
}

# 执行主函数
main "$@"
